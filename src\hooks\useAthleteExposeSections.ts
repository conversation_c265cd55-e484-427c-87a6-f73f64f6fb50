import { AppDispatch, RootState } from "@/store";
import {
  fetchAthleteToggleSections,
  putAthleteToggleSections,
} from "@/store/slices/athlete/athleteProfileSlice";
import {
  fetchAthleteSportSectionsHide,
  postAthleteSportsSectionHide,
} from "@/store/slices/athlete/athleteSportProfileSlice";
import { useDispatch, useSelector } from "react-redux";
import { useTokenValues } from "./useTokenValues";

export const useHandleAthleteSectionExpose = () => {
  const dispatch = useDispatch<AppDispatch>();
  const { userId } = useTokenValues();

  const {
    socialMediaToggle,
    quickLinksToggle,
    toggleAchievements,
    toggleGallery,
    profileToggle,
    physicalStatsToggle,
    toggleContactSection,
    toggleSchoolView,
    isProfileSearchCA,
  } = useSelector((state: RootState) => state.athleteProfile);

  const {
    toggleVideoSection,
    toggleHighlightLinks,
    toggleMileStone,
    toggleVictoryVault,
  } = useSelector((state: RootState) => state.athleteSportProfile);

  const handleToggleSections = async (key: string, checked: boolean) => {
    const payload = {
      userId,
      profile: key === "profile" ? checked : profileToggle,
      socialMedia: key === "socialMedia" ? checked : socialMediaToggle,
      gallery: key === "gallery" ? checked : toggleGallery,
      quickLinks: key === "quickLinks" ? checked : quickLinksToggle,
      physicalStats: key === "physicalStats" ? checked : physicalStatsToggle,
      achievements: key === "achievements" ? checked : toggleAchievements,
      parentGuardian: key === "parentGuardian" ? checked : toggleContactSection,
      allowCurrentSchoolToView:
        key === "allowCurrentSchoolToView" ? checked : toggleSchoolView,
      makeProfilePublicInCA:
        key === "isProfileSearchCA" ? checked : isProfileSearchCA,
      victoryVault: true,
      highlightVideo: true,
      highlightLinks: true,
    };

    try {
      const result = await dispatch(putAthleteToggleSections(payload));
      if (putAthleteToggleSections.fulfilled.match(result)) {
        await dispatch(fetchAthleteToggleSections());
      }
    } catch (err) {
      console.error("Toggle update failed:", err);
    }
  };

  const handleAthleteSportsSectionsHide = async (
    sportId: number,
    key: string,
    checked: boolean
  ) => {
    const payload = {
      userId,
      sportId,
      highlightVideos:
        key === "toggleVideoSection" ? checked : toggleVideoSection,
      highlightLinks:
        key === "toggleHighlightLinks" ? checked : toggleHighlightLinks,
      milestones: key === "toggleMileStone" ? checked : toggleMileStone,
      victoryVault: key === "toggleVictoryVault" ? checked : toggleVictoryVault,
    };

    try {
      const result = await dispatch(postAthleteSportsSectionHide(payload));
      if (postAthleteSportsSectionHide.fulfilled.match(result)) {
        await dispatch(fetchAthleteSportSectionsHide(sportId));
      }
    } catch (err) {
      console.error("Toggle update failed:", err);
    }
  };

  return {
    handleToggleSections,
    handleAthleteSportsSectionsHide,
  };
};
