
interface IProps {
    item: any
}
const NavItem = ({ item }: IProps) => {
    return (
        <>
            <li key={item?.id} className="flex flex-col justify-center cursor-pointer items-center text-slate-700">
                {item?.icon}
                <div className="flex items-center">
                    <span className="text-sm text-primary">{item?.label}</span>
                    <span>{item?.labelIcon}</span>
                </div>
            </li>
        </>
    )
}
export default NavItem