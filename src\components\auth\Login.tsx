"use client";
import {
  <PERSON><PERSON>,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger
} from "@/components/ui/dialog";
import { useTokenValues } from "@/hooks/useTokenValues";
import { AppDispatch, RootState } from "@/store";
import {
  sendOtp,
  updateTermsAcceptedAt,
  verifyOtpLogin,
} from "@/store/slices/auth/loginSlice";
import { ROLES } from "@/utils/constants";
import { Loader2, ShieldCheck } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";
import { toast } from "react-toastify";
import { Button } from "../ui/button";
import { Input } from "../ui/input";

interface LoginProps {
  defaultEmail?: string;
  openByDefault?: boolean;
}

const Login = ({ defaultEmail = "", openByDefault = false }: LoginProps) => {
  const dispatch = useDispatch<AppDispatch>();
  const router = useRouter();

  const { loading, error, user } = useSelector(
    (state: RootState) => state.login
  );

  const [isDialogOpen, setIsDialogOpen] = useState(openByDefault);
  const [email, setEmail] = useState(defaultEmail);
  const [emailError, setEmailError] = useState("");
  const [otpSent, setOtpSent] = useState(false);
  const [otp, setOtp] = useState(Array(6).fill(""));
  const [otpError, setOtpError] = useState("");
  const otpRefs = useRef<HTMLInputElement[]>([]);
  const [termsData, setTermsData] = useState<{
    title: string;
    content: string;
  } | null>(null);
  const [openTermsDialog, setOpenTermsDialog] = useState(false);
  const { userId, roleType, roleId } = useTokenValues()


  const validateEmail = (email: string) => {
    const regex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return regex.test(email);
  };

  const getCurrentESTDate = () => {
    const estDate = new Date().toLocaleString("en-US", {
      timeZone: "America/New_York", // EST/EDT depending on daylight savings
      year: "numeric",
      month: "long",
      day: "numeric",
    });
    return estDate;
  };

  const renderTermsAndConditions = () => {
    if (typeof window === "undefined") return;
    const userData: any = JSON.parse(localStorage.getItem("userInfo") || "{}");
    let roleId = userData?.roleId;
    let userType = "";
    switch (roleId) {
      case 2:
        userType = "athlete";
        break;
      case 3:
        userType = "coach";
        break;
      case 4:
        userType = "business";
        break;
      default:
        console.warn("Unknown roleId:", roleId);
        return;
    }
    const getTermsLink = () => {
      if (!userType) return "#";
      return `/terms/${userType}`; // Adjust this URL path based on your actual routing
    };

    const renderLink = (text: string) => (
      <a
        href={getTermsLink()}
        target="_blank"
        rel="noopener noreferrer"
        className="text-blue-400 underline cursor-pointer"
      >
        {text}
      </a>
    );

    switch (userType) {
      case "athlete":
        return (
          <p>
            I am 18 years or older or I am the parent/legal guardian of the
            athlete. I have read and agree to the{" "}
            {renderLink(
              "Connect Athlete Terms & Conditions for Athletes and Parents"
            )}
            .
          </p>
        );
      case "coach":
        return (
          <p>
            I have read and agree to the{" "}
            {renderLink("Connect Athlete Coach Terms & Conditions")}.
          </p>
        );
      case "business":
        return (
          <p>
            I am an authorized representative of this business. I have read and
            agree to the{" "}
            {renderLink(
              "Connect Athlete Terms & Conditions for Sports Businesses/Organizations"
            )}
            .
          </p>
        );
      default:
        return null;
    }
  };

  useEffect(() => {
    if (openByDefault && defaultEmail) {
      // handleSendOtp(); // Automatically sends OTP
    }
  }, [openByDefault, defaultEmail]);

  const handleSendOtp = async () => {
    setEmailError("");
    if (!email.trim()) {
      setEmailError("Email is required.");
      return;
    }
    if (!validateEmail(email)) {
      setEmailError("Please enter a valid email.");
      return;
    }

    try {
      const result = await dispatch(sendOtp(email)).unwrap();
      if (result?.user?.id && result?.user?.roleId) {
        setOtpSent(true);
        toast.success("OTP Sent to your email please check");
      }
    } catch (err) {
      console.error("OTP Error:", err);
    }
  };

  const handleVerifyOtp = async () => {
    setOtpError("");
    const code = otp.join("");

    if (code.length < 6) {
      setOtpError("Please enter a valid 6-digit OTP.");
      return;
    }

    if (!user) {
      setOtpError("User not found. Please try again.");
      return;
    }

    try {
      const result = await dispatch(
        verifyOtpLogin({ userId: user.id, roleId: user.roleId, otp: code })
      ).unwrap();
      if (result.status === 200) {
        toast.success("OTP verified successfully!");
        localStorage.setItem("userInfo", JSON.stringify(result?.user));
        localStorage.setItem("profileInfo", JSON.stringify(result?.profileData));
        localStorage.setItem("profileId", result?.profileData?.id);
        localStorage.setItem("token", result?.token);
        localStorage.setItem("userId", result?.user?.id);
        localStorage.setItem("roleId", result?.user?.roleId);
      }

      setIsDialogOpen(false);

      let userType = "";
      switch (user.roleId) {
        case 2:
          userType = "athlete";
          break;
        case 3:
          userType = "coach";
          break;
        case 4:
          userType = "business";
          break;
        default:
          console.warn("Unknown roleId:", user.roleId);
          return;
      }

      await fetchTermsAndConditions(userType);
    } catch (err) {
      console.error("OTP Verify Error:", err);
    }
  };

  const handleOtpChange = (index: number, value: string) => {
    if (!/^\d?$/.test(value)) return;
    const updated = [...otp];
    updated[index] = value;
    setOtp(updated);
    if (value && index < 5) otpRefs.current[index + 1]?.focus();
  };

  const fetchTermsAndConditions = async (userType: string) => {
    try {
      const response = await fetch(`/api/get-terms?type=${userType}`);
      const result = await response.json();
      if (result.status === 200) {
        setTermsData({
          title: result.data.title,
          content: result.data.content,
        });
        setOpenTermsDialog(true);
      }
    } catch (error) {
      console.error("Failed to fetch terms", error);
    }
  };

  const handleAcceptTerms = async () => {
    if (!userId) {
      console.error("User ID not found. Cannot update terms.");
      return;
    }

    try {
      const response = await dispatch(updateTermsAcceptedAt(userId)).unwrap();
      if (response.status === 200) {
        // success - do post-processing
        localStorage.setItem("acceptedTerms", "true");
        setOpenTermsDialog(false);

        switch (roleId) {
          case ROLES.ATHLETE:
            router.push("/athlete");
            break;
          case ROLES.COACH:
            router.push("/coach");
            break;
          case ROLES.BUSINESS:
            router.push("/business");
            break;
          default:
            // router.push("/");
            console.error("Unknown roleId. Cannot redirect.");
        }
      }
    } catch (error) {
      console.error("Error updating terms acceptance:", error);
    }
  };

  return (
    <>
      <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <DialogTrigger asChild>
          <Button
            variant="primaryNavGradient"
            className="text-primary font-bold hover:bg-secondary hover:text-white"
          >
            <ShieldCheck strokeWidth={3} />
            Login
          </Button>
        </DialogTrigger>
        <DialogContent className="bg-[#0a1832] text-white border-none">
          <DialogHeader className="space-y-6">
            <DialogTitle className="text-2xl font-bold text-center">
              Login
            </DialogTitle>
            <DialogDescription className="text-white">
              {!otpSent ? (
                <div className="flex flex-col gap-4">
                  <label>Email</label>
                  <Input
                    type="email"
                    placeholder="Enter your email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="bg-[#0f2a47] border border-gray-600 placeholder:text-gray-400 text-white"
                  />
                  {emailError && (
                    <p className="text-red-500 text-sm">{emailError}</p>
                  )}
                  <p className="text-gray-500 text-sm">Please check your registered email to get your OTP</p>
                  <div className="flex justify-center">
                    <Button
                      disabled={loading}
                      onClick={handleSendOtp}
                      variant="primaryGradient"
                      className="text-primary font-bold hover:bg-secondary hover:text-white"
                    >
                      {loading ? (
                        <Loader2 className="animate-spin" />
                      ) : (
                        "Send OTP"
                      )}
                    </Button>
                  </div>

                  {error && (
                    <p className="text-red-500 text-sm text-center">{error}</p>
                  )}
                </div>
              ) : (
                <div className="flex flex-col gap-4">
                  <label>Enter OTP</label>
                  <div className="flex gap-2 justify-center">
                    {otp.map((digit, idx) => (
                      <Input
                        key={idx}
                        maxLength={1}
                        value={digit}
                        ref={(el) => {
                          if (el) otpRefs.current[idx] = el;
                        }}
                        onChange={(e) => handleOtpChange(idx, e.target.value)}
                        className="w-10 h-12 text-center text-lg bg-[#0f2a47] text-white border border-gray-600"
                      />
                    ))}
                  </div>
                  {otpError && (
                    <p className="text-red-500 text-sm text-center">
                      {otpError}
                    </p>
                  )}
                  <Button
                    onClick={handleVerifyOtp}
                    disabled={loading}
                    variant="primaryGradient"
                    className="text-primary font-bold hover:bg-secondary hover:text-white"
                  >
                    {loading ? (
                      <Loader2 className="animate-spin" />
                    ) : (
                      "Verify OTP"
                    )}
                  </Button>
                  {error && (
                    <p className="text-red-500 text-sm text-center">{error}</p>
                  )}
                </div>
              )}
            </DialogDescription>
          </DialogHeader>
        </DialogContent>
      </Dialog>

      <Dialog open={openTermsDialog} onOpenChange={setOpenTermsDialog}>
        <DialogContent className="w-full max-w-md sm:max-w-xl p-6 bg-gray-900 text-white border-none flex flex-col gap-6">
          <DialogHeader>
            <DialogTitle className="text-white text-md">
              Before proceeding, please read the Terms & Conditions using the link below
            </DialogTitle>
            {/* <DialogDescription className="text-gray-400 text-sm">
              Please read and accept to continue
            </DialogDescription> */}
          </DialogHeader>
          <p className="text-xs text-gray-400 text-left">
            Last updated: {getCurrentESTDate()} (EST)
          </p>

          <div className="flex flex-col sm:flex-row justify-between items-center gap-4 border-t border-gray-700 pt-4">
            <p className="text-sm text-gray-300 text-center sm:text-left">
              {renderTermsAndConditions()}
            </p>
            <Button
              variant="primaryGradient"
              className="w-full sm:w-auto text-primary font-bold hover:bg-secondary hover:text-white"
              onClick={handleAcceptTerms}
            >
              Agree
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
};

export default Login;
