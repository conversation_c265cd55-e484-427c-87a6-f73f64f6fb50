import axiosInstance from "@/utils/axiosInstance";
import { EachSearchItem, Option } from "@/utils/interfaces";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";
import { toast } from "react-toastify";
import { ConnectedUser } from "@/types/messages";

interface CommonStates {
  apiStatus: string;
  loading: boolean;
  error: string;
  allStatesList: EachSearchItem[];
  allLocationsList: EachSearchItem[];
  allSportsList: EachSearchItem[];
  allSportLevelList: EachSearchItem[];
  allSpecilitiesList: EachSearchItem[];
  documentTypesList: Option[];
  allUniqueStrengthsList: Option[];
  allLearningTopicsList: Option[];
  allGraduationYears: Option[];
  allTagsList: Option[];
  schoolNamesList: Option[];
  athleteInterestsList: Option[];
  sportStatNamesList: Option[];
  fileUrl: string;
  allCountiesList: Option[];
  messagesConnectedUsers: ConnectedUser[];
}

const initialState: CommonStates = {
  apiStatus: "",
  loading: false,
  error: "",
  allStatesList: [],
  allLocationsList: [],
  allSportsList: [],
  allSportLevelList: [],
  allSpecilitiesList: [],
  documentTypesList: [],
  allUniqueStrengthsList: [
    { value: 1, label: "Skill Development" },
    { value: 2, label: "Confidence Building" },
    { value: 3, label: "Strength Conditioning" },
  ],
  allLearningTopicsList: [],
  allGraduationYears: [],
  allTagsList: [],
  schoolNamesList: [],
  athleteInterestsList: [],
  sportStatNamesList: [],
  allCountiesList: [],
  fileUrl: "",
  messagesConnectedUsers: [],
};

export const agesList = [
  { value: 1, label: "7-8" },
  { value: 2, label: "9-10" },
  { value: 3, label: "11-12" },
  { value: 4, label: "13-14" },
  { value: 5, label: "15-16" },
  { value: 6, label: "17-18" },
  { value: 7, label: "18+" },
];

export const genderList = [
  { value: 1, label: "Male" },
  { value: 2, label: "Female" },
];

export const academicStandingsList = [
  "Excellent",
  "Above Average",
  "On Track",
  "Needs Improvement",
  "Not Sure",
];

export const gradesList = [
  "2nd Grade",
  "3rd Grade",
  "4th Grade",
  "5th Grade",
  "6th Grade",
  "7th Grade",
  "8th Grade",
  "9th Grade (Freshman)",
  "10th Grade (Sophomore)",
  "11th Grade (Junior)",
  "12th Grade (Senior)",
  "13th Year / PG Year",
  "College Freshman",
  "College Sophomore",
  "College Junior",
  "College Senior",
  "Graduate Student",
  "Gap Year",
  "Other",
];

export const currentSeasonStatusList = [
  "Active",
  "Off-Season",
  "Off-Season Training",
  "Recreational",
];

export const relationShipList = [
  "Mother",
  "Father",
  "Legal Guardian",
  "Step-Parent",
  "Grand-Parent",
  "Older Sibling",
  "Aunt",
  "Uncle",
];

export const fetchAllStates = createAsyncThunk(
  "geoLocations/fetchAllStates",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_STATETAX_API_URL}/getallstates`
      );
      if (response.status === 200) {
        const formattedStates = response?.data?.data?.map((state) => ({
          value: state?.id,
          label: state?.name,
        }));
        return fulfillWithValue(formattedStates);
      }
    } catch (error) {
      console.error("Error fetching states:", error);
      rejectWithValue(error);
    }
  }
);

export const fetchAllLocations = createAsyncThunk(
  "geoLocations/fetchAllLocations",
  async (stateId: number, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_STATETAX_API_URL}/getallcities?stateId=${stateId}`
      );
      if (response.status === 200) {
        const formattedLocations = response?.data?.citiesData?.map((city) => ({
          value: city?.id,
          label: city?.name,
        }));
        const updatedLocations = [
          // { value: 0, label: "All Cities" },
          ...formattedLocations,
        ];
        return fulfillWithValue(updatedLocations);
      }
    } catch (error) {
      console.error("Error fetching cities:", error);
      rejectWithValue(error);
    }
  }
);

export const fetchAllCounties = createAsyncThunk(
  "counties/fetchAllCounties",
  async (stateId: number, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/counties/state/${stateId}`
      );
      if (response.status === 200) {
        const formattedLocations = response?.data?.data?.map((county) => ({
          value: county?.id,
          label: county?.name,
        }));
        return fulfillWithValue(formattedLocations);
      }
    } catch (error) {
      console.error("Error fetching cities:", error);
      rejectWithValue(error);
    }
  }
);

export const fetchAllSports = createAsyncThunk(
  "sports/fetchAllSports",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_SPORTS_API_URL}/getallsports`
      );
      const formattedSports = response?.data?.data.map((sport) => ({
        value: sport?.id,
        label: sport?.sportName,
      }));
      return fulfillWithValue(formattedSports);
    } catch (error) {
      console.error("Failed to fetch sports:", error);
      rejectWithValue(error);
      return [];
    }
  }
);

export const fetchAllSportLevels = createAsyncThunk(
  "sports/fetchAllSportLevels",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/sportslevel`
      );
      const formattedLevelsList = response?.data?.sportsLevels?.map((each) => ({
        value: each?.id,
        label: each?.sportLevel,
      }));
      return fulfillWithValue(formattedLevelsList);
    } catch (error) {
      console.error("Failed to fetch sport levels:", error);
      rejectWithValue(error);
      return [];
    }
  }
);

export const fetchAllSpecialities = createAsyncThunk(
  "sports/fetchAllSpecialities",
  async (sportId: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_SPORTS_API_URL}/getspecialitybysport?sportId=${sportId}`
      );
      if (response.status === 200) {
        const formattedLocations = response?.data?.data
          ?.flat()
          ?.map((specility) => ({
            value: specility?.id,
            label: specility?.specialityTitle,
          }));
        return fulfillWithValue(formattedLocations);
      }
    } catch (error) {
      console.error("Error fetching cities:", error);
      rejectWithValue(error);
    }
  }
);

export const fetchLearingTopics = createAsyncThunk(
  "sports/fetchLearingTopics",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/reference/topics`
      );
      const formattedList = response?.data?.data?.map((each) => ({
        value: each?.id,
        label: each?.topicLabel,
      }));
      return fulfillWithValue(formattedList);
    } catch (error) {
      console.error("Failed to fetch sport levels:", error);
      rejectWithValue(error);
      return [];
    }
  }
);

export const fetchGraduationYears = createAsyncThunk(
  "learning/fetchGraduationYears",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/reference/years`
      );
      const formattedList =
        response?.data?.data?.map((each) => ({
          value: each?.id,
          label: each?.year?.toString(),
        })) || [];
      return fulfillWithValue(formattedList);
    } catch (error) {
      console.error("Failed to fetch sport levels:", error);
      rejectWithValue(error);
      return [];
    }
  }
);

export const fetchTags = createAsyncThunk(
  "achievements/fetchTags",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/reference/tags`
      );
      const formattedList =
        response?.data?.data?.map((each) => ({
          value: each?.id,
          label: each?.tagValue,
        })) || [];
      return fulfillWithValue(formattedList);
    } catch (error) {
      console.error("Failed to fetch sport levels:", error);
      rejectWithValue(error);
      return [];
    }
  }
);

export const fetchAthleteSchoolNames = createAsyncThunk(
  "schoolNames/fetchAthleteSchoolNames",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/reference/schools`
      );
      const formattedList =
        response?.data?.data?.map((each) => ({
          value: each?.id,
          label: each?.schoolName,
        })) || [];
      return fulfillWithValue(formattedList);
    } catch (error) {
      console.error("Failed to fetch sport levels:", error);
      rejectWithValue(error);
      return [];
    }
  }
);

export const fetchAthletesInterests = createAsyncThunk(
  "interests/fetchAthletesInterests",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/interests`
      );
      const formatedList = response?.data?.interests?.map((each) => ({
        value: each?.id,
        label: each?.interestName,
      }));
      return fulfillWithValue(formatedList);
    } catch (error) {
      console.error("Failed to fetch sport levels:", error);
      rejectWithValue(error);
      return [];
    }
  }
);

export const fetchSportStatNames = createAsyncThunk(
  "sportStats/fetchSportStatNames",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/reference/stats`
      );
      const formatedList = response?.data?.data?.map((each) => ({
        value: each?.id,
        label: each?.statName,
      }));
      return fulfillWithValue(formatedList);
    } catch (error) {
      console.error("Failed to fetch sport levels:", error);
      rejectWithValue(error);
      return [];
    }
  }
);

export const putProfileUrl = createAsyncThunk(
  "profileUrl/putProfileUrl",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/profile-url/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const fileUploadToS3 = createAsyncThunk(
  "fileUpload/fileUploadToS3",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/upload-to-s3`,
        payload,
        {
          headers: {
            "Content-Type": "multipart/form-data",
            Accept: "application/json",
          },
        }
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
    }
  }
);

export const getCoachDocType = createAsyncThunk(
  "coachFocus/getdoctypes",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/getdoctypes`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }

  }
);

export const fetchMessagesData = createAsyncThunk(
  "messages/fetchMessages",
  async (_, { fulfillWithValue, rejectWithValue }) => {

    const userId = localStorage.getItem("userId") || "";
    const token = localStorage.getItem("token") || "";
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_NOTIFICATION_API_URL}/messages/connecteduser/${userId}`,
        {
          headers: {
            authorization: `${token}`,
            Accept: "application/json",
            "Content-Type": "application/json",
          },
        }
      );

      console.log("response messages", response);

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const fetchAllMessages = createAsyncThunk(
  "messages/fetchAllMessages",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId") || "";
    const token = localStorage.getItem("token") || "";
    try {
      const response = await axios.get(
        `${process.env.NEXT_PUBLIC_NOTIFICATION_API_URL}/messages/${userId}/${payload?.receiverId}`,
        {
          headers: {
            authorization: `${token}`,
            Accept: "application/json",
            "Content-Type": "application/json",
          },
        }
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

const commonSlice = createSlice({
  name: "commonSlice",
  initialState,
  reducers: {
    handleCommonSliceUpdateStates: (state, action) => {
      const { name, value } = action.payload;
      state[name] = value;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchAllStates.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchAllStates.fulfilled, (state, action) => {
        state.loading = false;
        state.allStatesList = action.payload;
      })
      .addCase(fetchAllStates.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchAllLocations.pending, (state) => {
        state.loading = true;
        state.allLocationsList = [];
      })
      .addCase(fetchAllLocations.fulfilled, (state, action) => {
        state.loading = false;
        state.allLocationsList = action.payload || [];
      })
      .addCase(fetchAllLocations.rejected, (state, action) => {
        state.loading = false;
        state.allLocationsList = [];
        state.error = action.payload as string;
      })
      .addCase(fetchAllSports.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchAllSports.fulfilled, (state, action) => {
        state.loading = false;
        state.allSportsList = action.payload || [];
      })
      .addCase(fetchAllSports.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchAllCounties.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchAllCounties.fulfilled, (state, action) => {
        state.loading = false;
        state.allCountiesList = action.payload || [];
      })
      .addCase(fetchAllCounties.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        state.allCountiesList = [];
      })
      .addCase(fetchAllSportLevels.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchAllSportLevels.fulfilled, (state, action) => {
        state.loading = false;
        state.allSportLevelList = action.payload || [];
      })
      .addCase(fetchAllSportLevels.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchAllSpecialities.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchAllSpecialities.fulfilled, (state, action) => {
        state.loading = false;
        state.allSpecilitiesList = action.payload || [];
      })
      .addCase(fetchAllSpecialities.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchLearingTopics.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchLearingTopics.fulfilled, (state, action) => {
        state.loading = false;
        state.allLearningTopicsList = action.payload || [];
      })
      .addCase(fetchLearingTopics.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchGraduationYears.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchGraduationYears.fulfilled, (state, action) => {
        state.loading = false;
        state.allGraduationYears = action.payload || [];
      })
      .addCase(fetchGraduationYears.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchTags.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchTags.fulfilled, (state, action) => {
        state.loading = false;
        state.allTagsList = action.payload || [];
      })
      .addCase(fetchTags.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchAthleteSchoolNames.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchAthleteSchoolNames.fulfilled, (state, action) => {
        state.loading = false;
        state.schoolNamesList = action.payload || [];
      })
      .addCase(fetchAthleteSchoolNames.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchAthletesInterests.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchAthletesInterests.fulfilled, (state, action) => {
        state.loading = false;
        state.athleteInterestsList = action.payload || [];
      })
      .addCase(fetchAthletesInterests.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(fetchSportStatNames.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchSportStatNames.fulfilled, (state, action) => {
        state.loading = false;
        state.sportStatNamesList = action.payload || [];
      })
      .addCase(fetchSportStatNames.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      })
      .addCase(putProfileUrl.pending, (state) => {
        state.loading = true;
      })
      .addCase(putProfileUrl.fulfilled, (state, action) => {
        state.loading = false;
        toast.success("Profile url updated successfully!");
      })
      .addCase(putProfileUrl.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update profile url"
        );
      })
      .addCase(fileUploadToS3.pending, (state) => {
        state.loading = true;
      })
      .addCase(fileUploadToS3.fulfilled, (state, action) => {
        state.loading = false;
        if (action.payload) state.fileUrl = action.payload?.url;
      })
      .addCase(fileUploadToS3.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });
    builder
      .addCase(getCoachDocType.pending, (state) => {
        state.loading = true;
      })
      .addCase(getCoachDocType.fulfilled, (state, action) => {
        state.loading = false;
        state.documentTypesList = action?.payload?.data?.map((each) => ({
          value: each?.id,
          label: each?.docTyp,
        }));;
      })
      .addCase(getCoachDocType.rejected, (state, action) => {
        state.loading = false;
        state.error = action.payload as string;
      });

    builder
      .addCase(fetchMessagesData.pending, (state) => {
        state.apiStatus = "plansLoading";
      })
      .addCase(fetchMessagesData.fulfilled, (state, action) => {
        state.apiStatus = "plansSuccess";
        state.messagesConnectedUsers = action.payload;
      })
      .addCase(fetchMessagesData.rejected, (state, action) => {
        state.apiStatus = "plansFailed";
        state.error = action.payload as string;
      })
  },
});

export const { handleCommonSliceUpdateStates } = commonSlice.actions;
export default commonSlice.reducer;
