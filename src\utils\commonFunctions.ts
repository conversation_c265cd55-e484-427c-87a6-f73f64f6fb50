import { UnifiedProfile } from "@/hooks/useLocalStoredInfo";
import { ROLE_NAMES } from "./constants";

// Helper function to generate profile URL
export const generateProfileUrl = (profileData: UnifiedProfile): string => {
  const roleId = localStorage.getItem("roleId");
  const roleName = ROLE_NAMES[roleId || profileData?.roleId];
  const firstName = profileData?.userFirstName?.toLowerCase() || "";
  const lastName = profileData?.userLastName?.toLowerCase() || "";
  const userId = profileData?.userId;

  return lastName
    ? `/${roleName}/${userId}-${firstName}-${lastName}`
    : `/${roleName}/${userId}-${firstName}`;
};

export const generateUpdateProfileUrl = (
  profileData: UnifiedProfile
): string => {
  const roleId = localStorage.getItem("roleId");
  const roleName = ROLE_NAMES[roleId || profileData?.roleId];
  const firstName = profileData?.userFirstName?.toLowerCase() || "";
  const lastName = profileData?.userLastName?.toLowerCase() || "";
  const userId = profileData?.userId;

  return lastName
    ? `/${roleName}/profile/${userId}-${firstName}-${lastName}`
    : `/${roleName}/profile/${userId}-${firstName}`;
};

export function extractUserIdFromSlug(slug: string): number | null {
  const match = slug?.match(/^(\d+)-/);
  return match ? Number(match[1]) : null;
}
