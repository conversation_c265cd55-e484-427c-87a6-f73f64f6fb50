"use client";

import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion";
import {
    DropdownMenu,
    DropdownMenuContent,
    DropdownMenuGroup,
    DropdownMenuItem,
    DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { AppDispatch } from "@/store";
import { logout } from "@/store/slices/auth/loginSlice";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { ReactNode } from "react";
import { useDispatch } from "react-redux";
import { toast } from "react-toastify";
import { NavListItem } from "../NavBar";

interface IProps {
    triggerMenu: ReactNode;
    menusList?: NavListItem[];
}

export default function DropdownMenusItem({ triggerMenu, menusList }: IProps) {
    const router = useRouter();
    const dispatch = useDispatch<AppDispatch>();

    const handleLogout = async () => {
        try {
            const resultAction = await dispatch(logout());
            if (resultAction.type === logout.type) {
                router.push("/");
                localStorage.clear();
            }
        } catch (error) {
            toast.error("Failed to logout");
        }
    };

    const handleClickMenu = async (id: string) => {
        if (id === "logout") {
            handleLogout();
        }
    };

    return (
        <DropdownMenu modal={false}>
            <DropdownMenuTrigger asChild>
                <span className="cursor-pointer">{triggerMenu}</span>
            </DropdownMenuTrigger>

            <DropdownMenuContent
                className="w-full max-h-[350px] overflow-y-auto lg:max-h-none scroll-smooth scrollbar-thin scrollbar-thumb-muted-foreground scrollbar-hide"
                side="bottom"
                align="center"
            >
                <DropdownMenuGroup>
                    {menusList?.map((each) => {
                        if (each.route) {
                            return (
                                <Link key={each.id} href={each.route}>
                                    <DropdownMenuItem
                                        onClick={() => handleClickMenu(each.id)}
                                        className="cursor-pointer p-3"
                                    >
                                        <div className="flex items-center cursor-pointer gap-4">
                                            <p className="h-7 md:h-5 w-7 md:w-5">
                                                {each.labelIcon ? each.labelIcon : each.icon}
                                            </p>
                                            <p className="text-md font-semibold hover:text-secondary">
                                                {each.label}
                                            </p>
                                        </div>
                                    </DropdownMenuItem>
                                </Link>
                            );
                        }

                        if (each.dropdownMenus) {                            
                            return (
                                <div key={each.id} className="w-full px-2 py-1 cursor-pointer">
                                    <Accordion type="single" collapsible className="w-full">
                                        <AccordionItem value={each.id}>
                                            <AccordionTrigger className="flex items-center justify-between w-full rounded-md p-2">
                                                <div className="flex cursor-pointer gap-3 items-center font-semibold">
                                                    {each.icon}
                                                    {each.label}
                                                </div>
                                            </AccordionTrigger>
                                            <AccordionContent className="pl-5 space-y-2">
                                                {each.dropdownMenus.map((sub) => (
                                                    <Link
                                                        key={sub.id}
                                                        href={sub.route ?? "#"}
                                                        className="flex cursor-pointer gap-3 items-center hover:text-secondary"
                                                    >
                                                        {sub.icon}
                                                        {sub.label}
                                                    </Link>
                                                ))}
                                            </AccordionContent>
                                        </AccordionItem>
                                    </Accordion>
                                </div>
                            );
                        }

                        return (
                            <DropdownMenuItem
                                key={each.id}
                                onClick={() => handleClickMenu(each.id)}
                                className="cursor-pointer p-3"
                            >
                                <div className="flex cursor-pointer items-center gap-4">
                                    <p className="h-7 md:h-5 w-7 md:w-5">
                                        {each.labelIcon ? each.labelIcon : each.icon}
                                    </p>
                                    <p className="text-md font-semibold hover:text-secondary">
                                        {each.label}
                                    </p>
                                </div>
                            </DropdownMenuItem>
                        );
                    })}
                </DropdownMenuGroup>
            </DropdownMenuContent>
        </DropdownMenu>
    );
}
