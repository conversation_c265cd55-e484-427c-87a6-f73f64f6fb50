"use client";

import {
  Send,
  Paperclip,
  X,
  FileText,
  Image as ImageIcon,
  File,
  Play,
} from "lucide-react";
import { useEffect, useRef, useState } from "react";
import Avatar from "../common/Avatar";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import io from "socket.io-client";
import { ChatMessage, ConnectedUser, FileAttachment } from "@/types/messages";

const SOCKET_SERVER_URL = process.env.NEXT_PUBLIC_MAIN_API_URL!;
const NOTIFICATION_API_URL =
  process.env.NEXT_PUBLIC_NOTIFICATION_API_URL!;

interface ChatAreaProps {
  selectedConversation?: ConnectedUser;
}

const ChatArea = ({ selectedConversation }: ChatAreaProps) => {

  // console.log(selectedConversation,"selectedConversation")
  const [socket, setSocket] = useState<any>(null);
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [conversation, setConversation] = useState<ConnectedUser | null>(
    selectedConversation || null
  );
  const [newMessage, setNewMessage] = useState("");
  const [selectedFile, setSelectedFile] =
    useState<FileAttachment | null>(null);
  const [isDragOver, setIsDragOver] = useState(false);
  const [fileError, setFileError] = useState<string | null>(null);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);

  const senderId =
    typeof window !== "undefined"
      ? sessionStorage.getItem("userId")
      : null;
  const userRole =
    typeof window !== "undefined"
      ? sessionStorage.getItem("userRole")
      : null;

  const receiverId = selectedConversation?.id?.toString() || "";
  const room1 = `${senderId}-${receiverId}`;
  const room2 = `${receiverId}-${senderId}`;


  console.log(senderId, "senderId")

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  };

  useEffect(() => {
    if (!senderId) return;

    // Connect socket
    const sock = io(SOCKET_SERVER_URL, {
      transports: ["polling", "websocket"],
    });

    sock.on("connect", () => {
      console.log("Connected to socket.io");
    });

    sock.emit("join", room1);
    sock.emit("join", room2);

    // Receive incoming message
    sock.on("receive_message", (data: any) => {
      console.log("Incoming message:", data);

      const newMsg: ChatMessage = {
        id: Date.now(), // Use timestamp as ID for now
        conversationId: 1, // Default conversation ID
        senderId: data.senderId,
        content: data.content,
        messageType: "text",
        replyToMessageId: null,
        createdAt: data.createdAt,
        isDeleted: false,
        attachmentUrl: data.attachment?.url || null,
        attachmentFileType: data.attachment?.type || null,
        attachmentFileName: data.attachment?.name || null,
        attachmentFileSize: data.attachment?.size || null,
        deletedAt: null,
        updatedAt: data.createdAt
      };

      setMessages((prev) => [...prev, newMsg]);
    });

    setSocket(sock);

    return () => {
      sock.emit("leave", room1);
      sock.emit("leave", room2);
      sock.disconnect();
    };
  }, [senderId, receiverId, room1, room2]);

  // Fetch message history
  useEffect(() => {
    if (!senderId) return;

    const fetchHistory = async () => {
      const res = await fetch(
        `${NOTIFICATION_API_URL}/messages/${senderId}/${receiverId}`
      );
      const data = await res.json();

      const formatted: ChatMessage[] = data.map((msg: any) => ({
        id: crypto.randomUUID(),
        content: msg.content,
        timestamp: new Date(msg.createdAt),
        senderName:
          msg.senderId === senderId
            ? "You"
            : `${msg.user?.firstName || ""} ${
                msg.user?.lastName || ""
              }`,
        attachment: msg.attachment
          ? {
              url: msg.attachment.url,
              name: msg.attachment.name,
              type: msg.attachment.type,
              size: msg.attachment.size,
            }
          : undefined,
      }));

      setMessages(formatted);

      if (data?.length > 0) {
        setConversation({
          id: receiverId,
          firstName: data[0]?.user?.firstName || "Unknown",
          lastName: data[0]?.user?.lastName || "User",
          email: "",
          isOnline: false,
          galleries: [],
        });
      }
    };

    fetchHistory();
  }, [senderId, receiverId]);

  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.readAsDataURL(file);
      reader.onload = () => resolve(reader.result as string);
      reader.onerror = error => reject(error);
    });
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!newMessage.trim() && !selectedFile) return;
    if (!socket || !senderId) return;

    // Prevent sending if there's a file error
    if (fileError) {
      alert("Please fix the file error before sending.");
      return;
    }

    let attachmentData: any = undefined;

    if (selectedFile) {
      // Convert image and PDF files to base64, keep video as URL
      if (selectedFile.type === "image" || selectedFile.type === "pdf") {
        try {
          console.log(`Converting ${selectedFile.type} file to base64:`, selectedFile.file.name);
          const base64Data = await convertFileToBase64(selectedFile.file);
          console.log(`Base64 conversion successful. Data length: ${base64Data.length} characters`);
          attachmentData = {
            data: base64Data, // base64 string for images and PDFs
            name: selectedFile.file.name,
            type: selectedFile.type,
            size: selectedFile.file.size,
          };
        } catch (error) {
          console.error("Error converting file to base64:", error);
          alert("Error processing file. Please try again.");
          return;
        }
      } else {
        // For videos and other files, use URL
        console.log(`Using URL for ${selectedFile.type} file:`, selectedFile.file.name);
        attachmentData = {
          url: selectedFile.url,
          name: selectedFile.file.name,
          type: selectedFile.type,
          size: selectedFile.file.size,
        };
      }
    }

    const newMsg = {
      content: newMessage.trim() || "📎 Attachment",
      senderId,
      receiverId,
      roleId: userRole,
      room: room1,
      createdAt: new Date().toISOString(),
      attachment: attachmentData,
    };

    // Log the payload to console
    console.log("Message payload being sent:", newMsg);

    socket.emit("createMessage", newMsg);

    // Clear input and file selection immediately
    // The message will be added to the UI when the server broadcasts it back via 'receive_message'
    setNewMessage("");
    setSelectedFile(null);
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Prevent default drag and drop behavior on the window
  useEffect(() => {
    const preventDefaults = (e: DragEvent) => {
      e.preventDefault();
      e.stopPropagation();
    };

    window.addEventListener('dragover', preventDefaults);
    window.addEventListener('drop', preventDefaults);

    return () => {
      window.removeEventListener('dragover', preventDefaults);
      window.removeEventListener('drop', preventDefaults);
    };
  }, []);

  const processFile = (file: File) => {
    // Clear any previous errors
    setFileError(null);

    const allowedTypes = [
      "image/jpeg",
      "image/png",
      "image/gif",
      "image/webp",
      "video/mp4",
      "application/pdf",
      "application/msword",
      "application/vnd.openxmlformats-officedocument.wordprocessingml.document",
      "application/vnd.ms-excel",
      "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
      "application/vnd.ms-powerpoint",
      "application/vnd.openxmlformats-officedocument.presentationml.presentation",
      "text/plain",
      "text/csv",
    ];

    if (!allowedTypes.includes(file.type) && !file.name.toLowerCase().endsWith(".mp4")) {
      alert(
        "File type not supported. Please select images, videos (MP4), PDFs, or documents."
      );
      return false;
    }

    // Determine file type first
    let fileType: "image" | "document" | "pdf" | "video" = "document";
    if (file.type.startsWith("image/")) {
      fileType = "image";
    } else if (file.type === "application/pdf") {
      fileType = "pdf";
    } else if (file.type === "video/mp4" || file.name.toLowerCase().endsWith(".mp4")) {
      fileType = "video";
    }

    // Check file size based on type
    const isVideo = fileType === "video";
    const maxSize = isVideo ? 100 * 1024 * 1024 : 20 * 1024 * 1024; // 100MB for videos, 20MB for others
    const maxSizeText = isVideo ? "100MB" : "20MB";

    if (file.size > maxSize) {
      if (isVideo) {
        // For videos, still create the file object but set an error
        const fileUrl = URL.createObjectURL(file);
        setSelectedFile({
          file,
          type: fileType,
          url: fileUrl,
        });
        setFileError(`Video file size must be less than ${maxSizeText}. Current size: ${(file.size / 1024 / 1024).toFixed(2)}MB`);
        return false;
      } else {
        alert(`File size must be less than ${maxSizeText}.`);
        return false;
      }
    }

    const fileUrl = URL.createObjectURL(file);
    setSelectedFile({
      file,
      type: fileType,
      url: fileUrl,
    });

    return true;
  };

  const handleFileSelect = (
    e: React.ChangeEvent<HTMLInputElement>
  ) => {
    const file = e.target.files?.[0];
    if (!file) return;

    processFile(file);

    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
  };

  const removeSelectedFile = () => {
    if (selectedFile) {
      URL.revokeObjectURL(selectedFile.url);
      setSelectedFile(null);
    }
    setFileError(null);
  };

  const handleDragOver = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  const handleDragEnter = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    // Only set drag over to false if we're leaving the main container
    if (e.currentTarget === e.target) {
      setIsDragOver(false);
    }
  };

  const handleDrop = (e: React.DragEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragOver(false);

    const files = Array.from(e.dataTransfer.files);
    if (files.length > 0) {
      const file = files[0]; // Only take the first file
      processFile(file);
    }
  };

  const getFileIcon = (type: string) => {
    if (type === "image")
      return <ImageIcon className="w-4 h-4" />;
    if (type === "pdf") return <FileText className="w-4 h-4" />;
    if (type === "video") return <Play className="w-4 h-4" />;
    return <File className="w-4 h-4" />;
  };

  const renderMessageWithLinks = (text: string) => {
    const urlRegex =
      /(https?:\/\/[^\s]+|www\.[^\s]+|[^\s]+\.[a-z]{2,}(?:\/[^\s]*)?)/gi;
    const parts = text.split(urlRegex);

    return parts.map((part, index) => {
      if (urlRegex.test(part)) {
        let url = part;
        if (
          !part.startsWith("http://") &&
          !part.startsWith("https://")
        ) {
          url = "https://" + part;
        }
        return (
          <a
            key={index}
            href={url}
            target="_blank"
            rel="noopener noreferrer"
            className="underline hover:no-underline font-medium"
            onClick={(e) => e.stopPropagation()}
          >
            {part}
          </a>
        );
      }
      return part;
    });
  };

  const formatMessageTime = (date: Date) => {
    return date.toLocaleTimeString([], {
      hour: "2-digit",
      minute: "2-digit",
    });
  };

  const formatMessageDate = (date: Date) => {
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);

    if (date.toDateString() === today.toDateString()) {
      return "Today";
    } else if (date.toDateString() === yesterday.toDateString()) {
      return "Yesterday";
    } else {
      return date.toLocaleDateString();
    }
  };

  const shouldShowDateSeparator = (
    currentMessage: ChatMessage,
    previousMessage?: ChatMessage
  ) => {
    if (!previousMessage) return true;

    const currentDate = new Date(
      currentMessage.timestamp
    ).toDateString();
    const previousDate = new Date(
      previousMessage.timestamp
    ).toDateString();

    return currentDate !== previousDate;
  };

  if (!conversation) return <p>Loading...</p>;

  return (
    <div
      className="flex flex-col h-full relative"
      onDragOver={handleDragOver}
      onDragEnter={handleDragEnter}
      onDragLeave={handleDragLeave}
      onDrop={handleDrop}
    >
      {/* Drag overlay */}
      {isDragOver && (
        <div className="absolute inset-0 bg-blue-500 bg-opacity-10 border-2 border-dashed border-blue-500 z-50 flex items-center justify-center">
          <div className="bg-white p-6 rounded-lg shadow-lg text-center">
            <div className="text-blue-500 mb-2">
              <Paperclip className="w-8 h-8 mx-auto" />
            </div>
            <p className="text-lg font-medium text-gray-900">Drop your file here</p>
            <p className="text-sm text-gray-500">Supports images, videos (MP4), PDFs, and documents</p>
            <p className="text-xs text-gray-400 mt-1">Max file size: 20MB (100MB for videos)</p>
          </div>
        </div>
      )}

      {/* Header */}
      <div className="p-4 border-b border-gray-200 bg-white">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className="relative">
              <Avatar
                profileImg={
                  conversation?.galleries[0]?.fileLocation
                }
                name={conversation.firstName.charAt(0)}
                styles="h-10 w-10 bg-slate-700 text-white"
              />

            </div>
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {conversation.firstName} {conversation.lastName}
              </h2>
            </div>
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 bg-gray-50">
        <div className="space-y-4">
          {messages.map((message, index) => {
            const prev =
              index > 0 ? messages[index - 1] : undefined;
            const showDateSeparator = shouldShowDateSeparator(
              message,
              prev
            );
            const isOwnMessage =
              message.senderName === "You";

            return (
              <div key={message.id}>
                {showDateSeparator && (
                  <div className="flex justify-center my-4">
                    <span className="px-3 py-1 text-xs text-gray-500 bg-white rounded-full border">
                      {formatMessageDate(message.timestamp)}
                    </span>
                  </div>
                )}
                <div
                  className={`flex ${
                    isOwnMessage
                      ? "justify-end"
                      : "justify-start"
                  }`}
                >
                  <div
                    className={`flex items-end space-x-2 max-w-xs lg:max-w-md ${
                      isOwnMessage
                        ? "flex-row-reverse space-x-reverse"
                        : ""
                    }`}
                  >
                    {!isOwnMessage ? (
                      <Avatar
                        profileImg={
                          conversation?.galleries[0]?.fileLocation
                        }
                        name={conversation.firstName.charAt(0)}
                        styles="h-8 w-8 bg-slate-700 text-white flex-shrink-0"
                      />
                    ) : (
                      <Avatar
                        profileImg=""
                        name="Y"
                        styles="h-8 w-8 bg-blue-600 text-white flex-shrink-0"
                      />
                    )}
                    <div
                      className={`px-4 py-2 rounded-2xl ${
                        isOwnMessage
                          ? "bg-blue-600 text-white rounded-br-md"
                          : "bg-white text-gray-900 border border-gray-200 rounded-bl-md"
                      }`}
                    >
                      {message.content &&
                        message.content !==
                          "📎 Attachment" && (
                          <p className="text-sm">
                            {renderMessageWithLinks(
                              message.content
                            )}
                          </p>
                        )}

                      {message.attachment && (
                        <div className="mt-2">
                          {message.attachment.type ===
                          "image" ? (
                            <img
                              src={message.attachment.url}
                              alt={message.attachment.name}
                              className="max-w-full max-h-48 rounded border cursor-pointer"
                              onClick={() =>
                                window.open(
                                  message.attachment?.url,
                                  "_blank"
                                )
                              }
                            />
                          ) : message.attachment.type === "video" ? (
                            <video
                              src={message.attachment.url}
                              controls
                              className="max-w-full max-h-48 rounded border"
                              preload="metadata"
                            >
                              Your browser does not support the video tag.
                            </video>
                          ) : (
                            <div
                              className={`flex items-center space-x-2 p-2 rounded border ${
                                isOwnMessage
                                  ? "border-blue-400 bg-blue-500"
                                  : "border-gray-300 bg-gray-50"
                              }`}
                            >
                              {getFileIcon(
                                message.attachment.type
                              )}
                              <div className="flex-1 min-w-0">
                                <p
                                  className={`text-xs font-medium truncate ${
                                    isOwnMessage
                                      ? "text-white"
                                      : "text-gray-900"
                                  }`}
                                >
                                  {message.attachment.name}
                                </p>
                                <p
                                  className={`text-xs ${
                                    isOwnMessage
                                      ? "text-blue-100"
                                      : "text-gray-500"
                                  }`}
                                >
                                  {(
                                    message.attachment.size /
                                    1024 /
                                    1024
                                  ).toFixed(2)}{" "}
                                  MB
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      )}
                      <p
                        className={`text-xs mt-1 ${
                          isOwnMessage
                            ? "text-blue-100"
                            : "text-gray-500"
                        }`}
                      >
                        {formatMessageTime(
                          message.timestamp
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input */}
      <div className="p-4 border-t border-gray-200 bg-white">
        {selectedFile && (
          <div className="mb-3 p-3 bg-gray-50 rounded-lg border border-gray-200">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-2">
                {getFileIcon(selectedFile.type)}
                <div>
                  <p className="text-sm font-medium text-gray-900">
                    {selectedFile.file.name}
                  </p>
                  <p className="text-xs text-gray-500">
                    {(
                      selectedFile.file.size /
                      1024 /
                      1024
                    ).toFixed(2)}{" "}
                    MB
                  </p>
                </div>
              </div>
              <Button
                type="button"
                variant="ghost"
                size="icon"
                onClick={removeSelectedFile}
                className="h-8 w-8 text-gray-500 hover:text-gray-700"
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
            {selectedFile.type === "image" && (
              <img
                src={selectedFile.url}
                alt="Preview"
                className="mt-2 max-w-full h-20 object-cover rounded border"
              />
            )}
            {selectedFile.type === "video" && (
              <div>
                <video
                  src={selectedFile.url}
                  className="mt-2 max-w-full h-20 rounded border"
                  preload="metadata"
                  muted
                >
                  Your browser does not support the video tag.
                </video>
                {fileError && (
                  <div className="mt-2 p-2 bg-red-50 border border-red-200 rounded text-red-700 text-xs">
                    <p className="font-medium">⚠️ {fileError}</p>
                    <p className="mt-1">Please select a smaller video file to continue.</p>
                  </div>
                )}
              </div>
            )}
          </div>
        )}
        <form
          onSubmit={handleSendMessage}
          className="flex items-center space-x-2"
        >
          <input
            ref={fileInputRef}
            type="file"
            onChange={handleFileSelect}
            className="hidden"
            accept="image/*,video/mp4,.mp4,.pdf,.doc,.docx,.xls,.xlsx,.ppt,.pptx,.txt,.csv"
          />
          <Button
            type="button"
            variant="ghost"
            size="icon"
            onClick={() => fileInputRef.current?.click()}
            className="text-gray-500 hover:text-gray-700"
            title="Attach file (Max 20MB, 100MB for videos)"
          >
            <Paperclip className="w-4 h-4" />
          </Button>
          <Input
            type="text"
            placeholder="Type your message..."
            value={newMessage}
            onChange={(e) => setNewMessage(e.target.value)}
            className="flex-1 bg-gray-50 border-gray-200 focus:bg-white"
          />
          <Button
            type="submit"
            variant="default"
            disabled={!!fileError}
            className={fileError ? "opacity-50 cursor-not-allowed" : ""}
          >
            <Send className="w-4 h-4" />
          </Button>
        </form>
        <p className="text-xs text-gray-500 mt-1 text-center">
          Supports images, videos (MP4), PDFs, and documents • Max: 20MB (100MB for videos) • Drag & drop files here
        </p>
      </div>
    </div>
  );
};

export default ChatArea;
