// Message types based on API response structure

export interface ChatMessage {
  id: number;
  conversationId: number;
  senderId: number;
  content: string;
  messageType: string;
  replyToMessageId: number | null;
  createdAt: string;
  isDeleted: boolean;
  attachmentUrl: string | null;
  attachmentFileType: string | null;
  attachmentFileName: string | null;
  attachmentFileSize: number | null;
  deletedAt: string | null;
  updatedAt: string;
}

export interface Gallery {
  fileLocation: string;
}

export interface ConnectedUser {
  id: number;
  email: string;
  roleId: number;
  profileInfo: number;
  galleries: Gallery[];
  firstName: string;
  lastName: string;
  lastMessage: ChatMessage;
  createdAt: string;
}

// File attachment types for UI
export interface FileAttachment {
  file: File;
  type: "image" | "document" | "pdf" | "video";
  url: string;
}
