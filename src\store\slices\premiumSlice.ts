import axiosInstance from "@/utils/axiosInstance";
import { PremiumStateModal, PromoCode } from "@/utils/interfaces";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";

const initialState: PremiumStateModal = {
  apiStatus: "",
  error: "",
  premiumPlansList: undefined,
  billingStatesList: [],
  selectedBillingState: null,
  promoCode: null,
  planSummaryData: null,
  transactionsList: undefined,
  userSubscriptionData: null,
};

export const fetchPremiumPlans = createAsyncThunk(
  "premiumAccess/fetchPremiumPlans",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const roleId = localStorage.getItem("roleId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_PAYMENT_API_URL}/subscriptionPlan?roleId=${roleId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const fetchBillingStates = createAsyncThunk(
  "states/fetchBillingStates",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const roleId = localStorage.getItem("roleId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_STATETAX_API_URL}/getallbillingstate?roleId=${roleId}`
      );
      if (response.status === 200) {
        const formattedList = response?.data?.data?.map((state) => ({
          value: state?.id,
          label: state?.stateName,
        }));
        return fulfillWithValue(response?.data?.data);
      }
    } catch (error) {
      console.error("Error fetching cities:", error);
      rejectWithValue(error);
    }
  }
);

export const fetchPlanSummaryById = createAsyncThunk(
  "planSummary/fetchPremiumPlans",
  async (planId: number, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_PAYMENT_API_URL}/subscriptionPlan?id=${planId}`
      );
      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const validatePromoCode = createAsyncThunk(
  "promocode/validatePromoCode",
  async (promocode: string, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_PAYMENT_API_URL}/validate-promocode?name=${promocode}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(
          response.data.message || "Invalid or error verifying promo code"
        );
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message ||
            "Network error or something went wrong"
        );
      }
    }
  }
);

export const postProcessPayment = createAsyncThunk(
  "processPayment/postProcessPayment",
  async (paymentInfo: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_PAYMENT_API_URL}/processPayment`,
        paymentInfo
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "unsuccessful");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message ||
            "Network error or something went wrong"
        );
      }
    }
  }
);

export const fetchTransactionsHistory = createAsyncThunk(
  "transactions/fetchTransactionsHistory",
  async (_, { fulfillWithValue, rejectWithValue }) => {
    const roleId = Number(localStorage.getItem("roleId"));
    const userId = Number(localStorage.getItem("userId"));

    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_PAYMENT_API_URL}/getTransactionHistory?roleId=${roleId}&userId=${userId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(
          response.data.message || "Faile to fetch transaction history"
        );
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(
          error.response.data?.error ||
            "Unexpected error occured while fetching transactions history"
        );
      }
    }
  }
);

// export const fetchUserSubscription = createAsyncThunk(
//   "subscription/fetchUserSubscription",
//   async (_, { fulfillWithValue, rejectWithValue }) => {
//     const roleId = Number(localStorage.getItem("roleId"));
//     const userId = Number(localStorage.getItem("userId"));

//     try {
//       const response = await axiosInstance.get(
//         `${process.env.NEXT_PUBLIC_PAYMENT_API_URL}/getUserSubscription?roleId=${roleId}&userId=${userId}`,
//       );

//       if (response.status === 200 || response.status === 201) {
//         return fulfillWithValue(response.data.data || response.data);
//       } else {
//         return rejectWithValue(
//           response.data.message || "Faile to fetch user subscription"
//         );
//       }
//     } catch (error: any) {
//       if (error.response) {
//         console.error("Server Error:", error.response);
//         return rejectWithValue(
//           error.response.data?.error ||
//             "Unexpected error occured while fetching user subscirption"
//         );
//       }
//     }
//   }
// );

const premiumSlice = createSlice({
  name: "premium",
  initialState,
  reducers: {
    handleUpdatePremiumInput: (state, action) => {
      const { name, value } = action.payload;
      state[name] = value;
    },
    resetPaymentStates: (state) => {
      state.selectedBillingState = null;
      state.promoCode = null;
    },
  },
  extraReducers: (builder) => {
    builder
      .addCase(fetchPremiumPlans.pending, (state) => {
        state.apiStatus = "plansLoading";
      })
      .addCase(fetchPremiumPlans.fulfilled, (state, action) => {
        state.apiStatus = "plansSuccess";
        state.premiumPlansList = action.payload;
      })
      .addCase(fetchPremiumPlans.rejected, (state, action) => {
        state.apiStatus = "plansFailed";
        state.error = action.payload as string;
      })
      .addCase(fetchBillingStates.fulfilled, (state, action) => {
        state.billingStatesList = action.payload;
      })
      .addCase(fetchBillingStates.rejected, (state, action) => {
        state.error = action.payload as string;
      })
      .addCase(fetchPlanSummaryById.pending, (state) => {
        state.apiStatus = "planSummaryLoading";
      })
      .addCase(fetchPlanSummaryById.fulfilled, (state, action) => {
        state.apiStatus = "planSummarySuccess";
        state.planSummaryData = action.payload && action.payload?.[0];
      })
      .addCase(fetchPlanSummaryById.rejected, (state, action) => {
        state.apiStatus = "planSummaryFailed";
        state.error = action.payload as string;
      })
      .addCase(validatePromoCode.pending, (state) => {
        state.apiStatus = "validatePromoLoading";
        state.promoCode = {
          ...state.promoCode,
          status: "Verifying...",
        } as PromoCode;
      })
      .addCase(validatePromoCode.fulfilled, (state, action) => {
        state.apiStatus = "validatePromoSuccess";
        state.promoCode = {
          ...state.promoCode,
          code: action.payload?.promocodeName,
          discountPrcnt: action.payload?.discountPct,
          status: "Your promo code is valid & applied",
          isVerified: true,
        };
      })
      .addCase(validatePromoCode.rejected, (state, action) => {
        state.apiStatus = "validatePromoFailed";
        state.error = action.payload as string;
        state.promoCode = {
          ...state.promoCode,
          isVerified: false,
          status:
            (action.payload as string) ||
            "Invalid or error verifying promo code",
        } as PromoCode;
      })
      .addCase(postProcessPayment.pending, (state) => {
        state.apiStatus = "postPaymentLoading";
      })
      .addCase(postProcessPayment.fulfilled, (state, action) => {
        state.apiStatus = "postPaymentSuccess";
        if (action?.payload) {
          const { token, user, profileData } = action?.payload;
          localStorage.setItem("token", token);
          localStorage.setItem("profileInfo", JSON.stringify(profileData));
          localStorage.setItem("profileId", profileData?.id);
          localStorage.setItem("userInfo", JSON.stringify(user));
          localStorage.setItem("userId", user?.id);
          localStorage.setItem("roleId", user?.roleId);
        }
      })
      .addCase(postProcessPayment.rejected, (state, action) => {
        state.apiStatus = "postPaymentFailed";
        state.error = action.payload as string;
      })
      .addCase(fetchTransactionsHistory.pending, (state) => {
        state.apiStatus = "fetchTransactionLoading";
      })
      .addCase(fetchTransactionsHistory.fulfilled, (state, action) => {
        state.apiStatus = "fetchTransactionSuccess";
        state.transactionsList = action.payload;
      })
      .addCase(fetchTransactionsHistory.rejected, (state, action) => {
        state.apiStatus = "fetchTransactionFailed";
        state.error = action.payload as string;
      });
  },
});

export const { handleUpdatePremiumInput, resetPaymentStates } =
  premiumSlice.actions;
export default premiumSlice.reducer;
