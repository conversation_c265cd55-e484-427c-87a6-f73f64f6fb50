"use client";
import { getDecodedToken } from "@/hooks/useTokenValues";
import { AppDispatch, RootState } from "@/store";
import { logout } from "@/store/slices/auth/loginSlice";
import { usePathname, useRouter } from "next/navigation";
import { useEffect, useMemo } from "react";
import { useDispatch, useSelector } from "react-redux";

export const AuthProvider = ({ children }: { children: React.ReactNode }) => {
    const router = useRouter();
    const pathname = usePathname();
    const dispatch = useDispatch<AppDispatch>();
    const { token } = useSelector((state: RootState) => state.login);

    // ✅ Public route patterns (supporting subpaths for terms & privacy-policy)
    const publicRoutes = useMemo(
        () => [
            /^\/$/, // home
            /^\/privacy-policy(?:\/.*)?$/, // privacy policy & subpages
            /^\/terms(?:\/.*)?$/, // terms & subpages
            /^\/athlete\/\d+-[a-zA-Z-]+(?:\/.*)?$/, // athlete profile + subpaths
            /^\/athlete\/specials(?:\/.*)?$/, // specials
        ],
        []
    );

    const isPublicRoute = (path: string) =>
        publicRoutes.some((route) => route.test(path));

    const signOutAndRedirectToHome = () => {
        dispatch(logout());
        localStorage.clear();
        router.replace("/");
    };

    useEffect(() => {
        const storedToken = token || localStorage.getItem("token");
        const decoded = storedToken ? getDecodedToken(storedToken) : null;
        const now = Date.now() / 1000;

        // ✅ If token expired → logout
        if (decoded?.exp && decoded.exp < now) {
            signOutAndRedirectToHome();
            return;
        }

        // ✅ Only redirect if route is private and no token
        if (!isPublicRoute(pathname) && !storedToken) {
            signOutAndRedirectToHome();
        }
    }, [token, pathname]);

    return <>{children}</>;
};
