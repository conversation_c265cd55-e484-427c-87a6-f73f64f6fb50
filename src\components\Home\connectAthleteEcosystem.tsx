"use client";
import useEmblaCarousel from "embla-carousel-react";
import { useEffect, useRef, useState, useCallback } from "react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import parse from "html-react-parser";
import {
  titleParseOptions,
  shortDescriptionParseOptions,
  fileTitleParseOptions,
  fileDescriptionParseOptions,
} from "@/utils/parseOptions";
import { ChevronLeft, ChevronRight } from "lucide-react";

const ConnectAthleteEcosystem = () => {
  const [isHovered, setIsHovered] = useState(false);
  const sectionRef = useRef<HTMLDivElement | null>(null);
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true, align: "start" });

  const {
    connectAthleteEcoSystemSection,
    connectAthleteEcoSystemImages,
    loading,
  } = useSelector((state: RootState) => state.homeCMS);

  // Equal height logic
  const cardRefs = useRef<HTMLDivElement[]>([]);
  const setCardRef = (el: HTMLDivElement | null, i: number) => {
    if (el) cardRefs.current[i] = el;
  };

  const equalizeHeights = useCallback(() => {
    const els = cardRefs.current.filter(Boolean);
    if (!els.length) return;
    els.forEach((el) => (el.style.minHeight = "0px"));
    const max = Math.max(...els.map((el) => el.scrollHeight));
    els.forEach((el) => (el.style.minHeight = `${max}px`));
  }, []);

  useEffect(() => {
    const els = cardRefs.current.filter(Boolean);
    if (!els.length) return;
    const ro = new ResizeObserver(() => equalizeHeights());
    els.forEach((el) => ro.observe(el));

    equalizeHeights();
    const onR = () => equalizeHeights();
    window.addEventListener("resize", onR);

    return () => {
      window.removeEventListener("resize", onR);
      ro.disconnect();
    };
  }, [connectAthleteEcoSystemImages, equalizeHeights]);

  // Autoplay
  useEffect(() => {
    if (!emblaApi || isHovered) return;
    const autoplay = setInterval(() => emblaApi.scrollNext(), 5000);
    return () => clearInterval(autoplay);
  }, [emblaApi, isHovered]);

  // Arrow handlers
  const scrollPrev = () => emblaApi && emblaApi.scrollPrev();
  const scrollNext = () => emblaApi && emblaApi.scrollNext();

  if (
    loading ||
    !connectAthleteEcoSystemSection ||
    connectAthleteEcoSystemImages.length === 0
  )
    return null;

  return (
    <section
      ref={sectionRef}
      id="ecosystem"
      className="py-20 bg-[#0D1D3A] px-4 md:px-16 lg:px-36 xl:px-56 relative"
    >
      <div className="text-center mb-16">
        <h2 className="text-3xl text-center font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-6">
          {parse(connectAthleteEcoSystemSection.title || "", titleParseOptions)}
        </h2>
        <p className="max-w-3xl mx-auto text-gray-300 text-lg font-medium">
          {parse(
            connectAthleteEcoSystemSection.description || "",
            shortDescriptionParseOptions
          )}
        </p>
      </div>

      <div className="relative">
        {/* Left Arrow */}
        <button
          onClick={scrollPrev}
          className="absolute left-2 top-1/2 -translate-y-1/2 z-50 bg-white/10 backdrop-blur-sm hover:bg-black/60 text-white rounded-full w-8 h-8 flex items-center justify-center"
        >
         <ChevronLeft size={20} className="text-white" />
        </button>

        {/* Carousel */}
        <div
          className="overflow-hidden"
          ref={emblaRef}
          onMouseEnter={() => setIsHovered(true)}
          onMouseLeave={() => setIsHovered(false)}
        >
          <div className="flex gap-6 items-start">
            {connectAthleteEcoSystemImages.map((card, i) => (
              <div
                key={card.id}
                className="min-w-[85%] md:min-w-[50%] lg:min-w-[33%] flex"
              >
                <div
                  ref={(el) => setCardRef(el, i)}
                  className="relative rounded-3xl overflow-hidden shadow-lg border border-white/10 group bg-[#13294B] hover:shadow-orange-500/20 transition-shadow duration-500 w-full flex flex-col"
                >
                  <div className="relative z-20 flex-1 flex flex-col justify-start p-6 text-center text-white gap-4">
                    <h3 className="text-xl md:text-2xl xl:text-3xl font-bold">
                      {parse(card.fileTitle || "", fileTitleParseOptions)}
                    </h3>
                    <p className="text-gray-200 text-base md:text-lg xl:text-xl leading-relaxed">
                      {parse(
                        card.fileDescription || "",
                        fileDescriptionParseOptions
                      )}
                    </p>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Right Arrow */}
        <button
          onClick={scrollNext}
          className="absolute right-2 top-1/2 -translate-y-1/2 z-50 bg-white/10 backdrop-blur-sm hover:bg-black/60 text-white rounded-full w-8 h-8 flex items-center justify-center"
        >
          <ChevronRight size={20} className="text-white" />
        </button>
      </div>
    </section>
  );
};

export default ConnectAthleteEcosystem;
