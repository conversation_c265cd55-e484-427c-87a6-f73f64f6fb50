"use client";

import ClientGuard from "@/components/ClientGuard";
import MessagesScreen from "@/components/messages/MessagesScreen";
import { ROLES } from "@/utils/constants";

const AtheleteMessagesPage = () => {


  return (
    <ClientGuard allowedRoles={[ROLES.ATHLETE]}>
      <>
        <div className="h-[calc(100vh-5rem)] w-full">
          <MessagesScreen />
        </div>
      </>
    </ClientGuard>
  )
};

export default AtheleteMessagesPage;
