import ClientGuard from "@/components/ClientGuard"
import LeftFilterPanel from "@/components/adminSpecials/LeftFilterPanel"
import SearchAndSortHeader from "@/components/adminSpecials/SearchAndSortHeader"
import SpecialItem from "@/components/adminSpecials/SpecialItem"
import RightPanel from "@/components/athleteHome/RightPanel"

const specialsList = [
    {
        id: 1,
        author: "<PERSON>",
        role: "Product Designer at Designify",
        content: "Excited to share our latest UI kit!",
        time: "2h",
        avatar: "/user.svg",
        date: "June 5, 2025"
    },
    {
        id: 2,
        author: "<PERSON>",
        role: "Software Engineer at DevCorp",
        content: "Check out my new blog on microservices.",
        time: "5h",
        avatar: "/user.svg",
        date: "June 5, 2025"
    },
    {
        id: 3,
        author: "<PERSON>",
        role: "Software Engineer at DevCorp",
        content: "Check out my new blog on microservices.",
        time: "5h",
        avatar: "/user.svg",
        date: "June 5, 2025"
    },
    {
        id: 4,
        author: "<PERSON>",
        role: "Software Engineer at DevCorp",
        content: "Check out my new blog on microservices.",
        time: "5h",
        avatar: "/user.svg",
        date: "June 5, 2025"
    }
]

const SearchAdminSpecials = () => {
    return (
        <ClientGuard allowedRoles={[2]}>
            <div className="min-h-screen bg-gray-50 p-4">
                <div className="max-w-7xl mx-auto flex flex-col md:flex-row md:space-x-6">
                    {/* Left Filter Panel */}
                    <aside className="md:w-1/4 mb-6 md:mb-0">
                        <LeftFilterPanel />
                    </aside>

                    {/* Main Feed */}
                    <main className="md:w-2/4 space-y-6 p-5 sm:px-4 bg-white rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                            <h2 className="text-lg md:text-xl font-semibold text-blue-900">Explore Offers, Events & Tools for Your Success</h2>
                        </div>
                        <SearchAndSortHeader />
                        {specialsList?.map((item) => (
                            <SpecialItem key={item.id} {...item} />
                        ))}
                    </main>

                    {/* Right Panel */}
                    <aside className="md:w-1/4 mt-6 mb-6 md:mt-0">
                        <div className="sticky top-20">
                            <RightPanel />
                        </div>
                    </aside>
                </div>
            </div>
        </ClientGuard>
    )
}
export default SearchAdminSpecials