import { AddedStateLocationsItem } from "@/utils/interfaces";
import { Trash2 } from "lucide-react";
import { useState } from "react";
import { Button } from "../ui/button";
import AlertPopup from "./AlertPopup";

interface IProps {
    item: AddedStateLocationsItem;
    handleDelete: (id: number) => void
}

const CountyWithCities = ({ county }) => {
    const [showAll, setShowAll] = useState(false);
    const filteredCities = county?.cities?.filter(city => city?.cityName !== "All") || [];
    const displayCities = showAll ? filteredCities : filteredCities?.slice(0, 10);

    return (
        <div className="flex items-center px-4">
            <p className="text-wrap font-semibold">
                {(county?.countyName && county?.countyName !== 'All') ? `${county?.countyName} - ` : ''}

                <span className="font-light">
                    {displayCities.map(city => city.cityName).join(" | ")}
                    {filteredCities.length > 10 && (
                        <button
                            onClick={() => setShowAll(prev => !prev)}
                            className="text-blue-500 hover:underline ml-2"
                        >
                            {showAll ? "Show Less..." : "Show More..."}
                        </button>
                    )}
                </span>
            </p>
        </div>
    );
}


const StateLocationItem = ({ item, handleDelete }: IProps) => {
    return (
        <>
            <li
                className="grid grid-cols-4 w-full items-center justify-between gap-6 flex-wrap bg-white shadow-md hover:shadow-lg hover:border-slate-300 rounded-lg py-2 px-2"
                key={item?.stateId}
            >
                <div className="col-span-3 flex flex-col gap-1">
                    <h6 className="text-wrap font-semibold text-secondary px-4">
                        <span className="font-semibold ">{item?.stateName}</span>
                    </h6>
                    {item?.counties?.map((county) => (
                        <CountyWithCities key={county.countyId} county={county} />
                    ))}
                </div>

                <div className="col-span-1 flex items-center justify-end">
                    <AlertPopup
                        trigger={<Button variant="destructive" size="icon"><Trash2 /></Button>}
                        alertTitle="Delete Confirmation"
                        alertContent="Are you sure, you want to delete these locations?"
                        action={() => handleDelete(item?.stateId)}
                    />
                </div>
            </li>
        </>
    )
}
export default StateLocationItem