import { RootState } from "@/store";
import { ROLES } from "@/utils/constants";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useSelector } from "react-redux";
import { useTokenValues } from "./useTokenValues";

export type User = {
  id: number;
  email: string;
  is_hidden_email: boolean;
  mobile: string;
  is_hidden_mobile: boolean;
  profileInfo: number;
  profileUrl: string;
  hideProfileUrl: boolean;
  passcode_url: null;
  IsApproved: string;
  isActive: boolean;
  email_verified: string;
  last_email_verified_dt: string;
  sign_up_dt: string;
  IsSearchable: boolean;
  IsSubscription: string;
  subscription_set_by_admin: string;
  lastTermsAcceptedAt: string;
  last_profile_url_update_dt: null;
  pinCode: null;
  profileImg: string;
  roleId: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
  createdUser: number;
  updatedUser: number;
};

export type AthleteProfile = {
  id: number;
  age: number;
  gender: string;
  bio: string;
  achievements: string;
  preferredAthleteName: string;
  lastName: string;
  parentFirstName: string;
  parentLastName: string;
  userId: number;
  createdUser: number;
  updatedUser: number;
  referralCodeUsedId: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
  roleId: number;
};

export type CoachProfile = {
  id: number;
  firstName: string | null;
  lastName: string | null;
  bio: string;
  achievements: string;
  lookingFor: string;
  genderYouCoach: string;
  virtualTraining: string;
  roleId: number;
  userId: number;
  blurb: null | string;
  displayNameOption: null | string;
  coachIntroVideo: null | string;
  hideCoachIntroVideo: boolean;
  coachIntroBio: null | string;
  hideCoachBio: boolean;
  coachWebsite: null | string;
  hideCoachWebsite: boolean;
  verifiedStatus: string;
  websiteLink: string;
  eliteStatus: string;
  createdUser: number;
  updatedUser: number;
  referralCodeId: number;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
};

export type BusinessProfile = {
  id: number;
  organizationName: string;
  contactFirstName: string;
  contactLastName: string | null;
  title: string;
  bio: string;
  websiteLink: string;
  virtualTraining: string;
  businessTypeId: number;
  roleId: number;
  userId: number;
  createdUser: number;
  updatedUser: number;
  referralCodeUsedId: null | number;
  createdAt: string;
  updatedAt: string;
  deletedAt: null;
};

export type ProfileData = {
  userFirstName: string;
  userLastName: string;
};

export type UnifiedAthleteProfile = ProfileData & AthleteProfile;
export type UnifiedCoachProfile = ProfileData & CoachProfile;
export type UnifiedBusinessProfile = ProfileData & BusinessProfile;

export type UnifiedProfile =
  | UnifiedAthleteProfile
  | UnifiedCoachProfile
  | UnifiedBusinessProfile;

export function useLocalStoredInfo() {
  const [profileId, setProfileId] = useState<number | null>(null);
  const [profileData, setProfileData] = useState<UnifiedProfile | null>(null);
  const [userInfo, setUserInfo] = useState<User | null>(null);
  const router = useRouter();
  const reduxProfileData = useSelector(
    (state: RootState) => state.login.profileData
  );
  const { user } = useSelector((state: RootState) => state.login);
  const { roleId } = useTokenValues();

  function normalizeProfile(
    profile: AthleteProfile | CoachProfile | BusinessProfile,
    roleId: number
  ): UnifiedProfile {
    switch (roleId && Number(roleId)) {
      case ROLES.ATHLETE:
        const athleteProfile = profile as AthleteProfile;
        return {
          ...athleteProfile,
          userFirstName: athleteProfile?.preferredAthleteName,
          userLastName: athleteProfile?.lastName,
        };
      case ROLES.COACH:
        const coachProfile = profile as CoachProfile;
        return {
          ...coachProfile,
          userFirstName: coachProfile?.firstName!,
          userLastName: coachProfile?.lastName!,
        };
      case ROLES.BUSINESS:
        const businessProfile = profile as BusinessProfile;
        return {
          ...businessProfile,
          userFirstName: businessProfile?.organizationName,
          userLastName: "",
        };
      default:
        return {
          ...profile,
          userFirstName: "",
          userLastName: "",
        };
    }
  }

  useEffect(() => {
    if (typeof window !== "undefined") {
      const storedRoleId = roleId || Number(localStorage.getItem("roleId"));
      const storedUserData =
        user || JSON.parse(localStorage.getItem("userInfo") || "{}");
      const storedProfileData =
        reduxProfileData ||
        JSON.parse(localStorage.getItem("profileInfo") || "{}");
      setProfileId(storedProfileData?.id);
      setUserInfo(storedUserData);
      const normalized = normalizeProfile(storedProfileData, storedRoleId);
      setProfileData(normalized);
    }
  }, [router, reduxProfileData, user, roleId]);

  return { profileId, profileData, userInfo };
}
