"use client";
import React from "react";
import {
  Linkedin,
  X,
  Instagram,
  Info,
  Globe2,
  Workflow,
  Users,
} from "lucide-react";

const navList = [
  { id: 1, name: "About", route: "#about", icon: <Info size={20} /> },
  {
    id: 2,
    name: "Our Ecosystem",
    route: "#ecosystem",
    icon: <Globe2 size={20} />,
  },
  {
    id: 3,
    name: "Our Coach Network",
    route: "#ourCoach",
    icon: <Workflow size={20} />,
  },
];

const Footer = () => {
  const handleNavClick = (e: any, route: string) => {
    e.preventDefault();
    const el = document.querySelector(route);
    if (el) {
      el.scrollIntoView({ behavior: "smooth", block: "start" });
    }
  };

  return (
    <footer className="bg-[#0D2742] text-white py-20 px-3 sm:px-8 md:px-16 lg:px-36 xl:px-56">
      <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-12">
        {/* <PERSON><PERSON> + <PERSON><PERSON> */}
        <div className="space-y-4">
          <img
            src="/connectathlete-logo.svg"
            alt="Connect Athlete Logo"
            className="w-48 h-18"
          />
          <h3 className="text-xl font-bold text-[#F58220]">
            Empower. Engage. Elevate.
          </h3>
          <div className="flex gap-4 pt-2">
            <a
              href="https://www.instagram.com/connectathlete/"
              target="_blank"
              rel="noreferrer"
            >
              <Instagram className="w-6 h-6 text-white hover:text-[#F58220]" />
            </a>
            <a
              href="https://x.com/ConnectAthlete_"
              target="_blank"
              rel="noreferrer"
            >
              <X className="w-6 h-6 text-white hover:text-[#F58220]" />
            </a>
            <a
              href="https://www.linkedin.com/company/*********/admin/dashboard/"
              target="_blank"
              rel="noreferrer"
            >
              <Linkedin className="w-6 h-6 text-white hover:text-[#F58220]" />
            </a>
          </div>
        </div>

        {/* Address */}
        <div>
          <h4 className="text-lg font-semibold text-[#F58220] mb-4">
            Our Address
          </h4>
          <p>
            300 Witherspoon St, Suite 201
            <br />
            Princeton, NJ 08542
          </p>
          <p className="mt-2 mb-3 font-semibold"><EMAIL></p>
          <a
            href="/privacy-policy"
            target="_blank"
            rel="noopener noreferrer"
            className="mt-2 text-white hover:text-[#F58220] transition-colors duration-200"
          >
            Privacy Policy
          </a>
        </div>

        {/* Quick Links */}
        <div>
          <h4 className="text-lg font-semibold text-[#F58220] mb-4">
            Quick Links
          </h4>
          <ul className="space-y-2">
            {navList.map((item) => (
              <li key={item.id}>
                <a
                  href={item.route}
                  onClick={(e) => handleNavClick(e, item.route)}
                  className="text-white hover:text-[#F58220] transition-colors duration-200"
                >
                  {item.name}
                </a>
              </li>
            ))}
            <li className="mt-2 font-bold text-white">
              <a
                href="#registration"
                onClick={(e) => handleNavClick(e, "#registration")}
                className="hover:text-[#F58220] transition-colors duration-200 cursor-pointer"
              >
                Sign up For Free
              </a>
            </li>
          </ul>
        </div>

        {/* Terms */}
        <div>
          <h4 className="text-lg font-semibold text-[#F58220] mb-4">
            Terms & Conditions
          </h4>
          <ul className="space-y-2">
            <li>
              <a
                href="/terms/coach"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-[#F58220] transition-colors duration-200"
              >
                Coaches
              </a>
            </li>
            <li>
              <a
                href="/terms/business"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-[#F58220] transition-colors duration-200"
              >
                Sports Businesses / Organizations
              </a>
            </li>
            <li>
              <a
                href="/terms/athlete"
                target="_blank"
                rel="noopener noreferrer"
                className="text-white hover:text-[#F58220] transition-colors duration-200"
              >
                Parents / Legal Guardians / Athletes 18+
              </a>
            </li>
          </ul>
        </div>
      </div>

      {/* Copyright */}
      <div className="text-center mt-12 text-sm text-white border-t border-[#173251] pt-6">
        © 2025 Connect Athlete. All rights reserved.
        <br />
        Connect Athlete™ and all related names, content, and technologies are
        trademarks and intellectual property of Connect Athlete.
      </div>
    </footer>
  );
};

export default Footer
