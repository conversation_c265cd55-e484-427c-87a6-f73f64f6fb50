import { Label } from "@/components/ui/label"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { AppDispatch, RootState } from "@/store"
import { fetchAthleteLearningHighLights, handleUpdateUserInput, postAthleteLearningHighLight } from "@/store/slices/athlete/athleteProfileSlice"
import { academicStandingsList, fetchAthleteSchoolNames, fetchGraduationYears, fetchLearingTopics, gradesList } from "@/store/slices/commonSlice"
import { Option } from "@/utils/interfaces"
import { Loader } from "lucide-react"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import MultiSelectWithChip from "../common/MultiSelectWithChip"
import NumericInput from "../common/NumericInput"
import SearchInput from "../common/SearchInput"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Separator } from "../ui/separator"
import { Textarea } from "../ui/textarea"

const LearningHighLights = () => {
    const { allLearningTopicsList, allGraduationYears, schoolNamesList } = useSelector((state: RootState) => state.commonSlice)
    const { athleteLearning, selectedLearingTopicsList, apiStatus } = useSelector((state: RootState) => state.athleteProfile)
    const dispatch = useDispatch<AppDispatch>()

    const initialFetches = async () => {
        await dispatch(fetchAthleteSchoolNames())
        await dispatch(fetchGraduationYears())
        await dispatch(fetchLearingTopics())
        await dispatch(fetchAthleteLearningHighLights())
    }

    useEffect(() => {
        initialFetches()
    }, [dispatch])

    const handleOnChange = (name, value) => {
        dispatch(handleUpdateUserInput({ name: 'athleteLearning', value: { ...athleteLearning, [name]: value } }))
    }

    const handleMultiSelectChange = (name: string, value: Option[]) => {
        dispatch(handleUpdateUserInput({ name, value }))
    }


    const handleSave = async () => {
        const payload = {
            // schoolType: '', //not there in UI
            schoolId: athleteLearning?.currentSchoolName?.value,
            currentSchoolName: athleteLearning?.otherSchoolName,
            gradYear: athleteLearning?.graduationYear,
            overallProgress: athleteLearning?.overallAcademic,
            moreAbtOverallProgress: athleteLearning?.progress,
            gpa: athleteLearning?.gpa && Number(athleteLearning?.gpa),
            satScore: athleteLearning?.sat && Number(athleteLearning?.sat),
            actScore: athleteLearning?.act && Number(athleteLearning?.act),
            yrInSchool: athleteLearning?.grade,
            topicStrengthkey: JSON.stringify(selectedLearingTopicsList?.map(each => each.value)),
            topicClusterkey: '1',
            academicMmt: athleteLearning?.academicMmt
        }

        const learningAction = await dispatch(postAthleteLearningHighLight(payload))
        if (postAthleteLearningHighLight?.fulfilled?.match(learningAction)) {
            dispatch(handleUpdateUserInput({ name: 'athleteLearning', value: null }))
            await dispatch(fetchAthleteLearningHighLights())
        }
    }

    return (
        <>
            <div className="flex flex-col items-center gap-5 bg-slate-100 p-4 rounded-lg">
                <h3 className="font-bold text-xl text-wrap">Learning Highlights</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-5 w-full px-5">
                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">Current School Name</Label>
                        <SearchInput
                            list={schoolNamesList || []}
                            name="currentSchoolName"
                            placeholder="Select School Name..."
                            value={athleteLearning?.currentSchoolName}
                            onChange={handleOnChange}
                        />
                    </div>

                    {athleteLearning?.currentSchoolName?.label === 'School Not Found' &&
                        <div className="flex flex-col gap-1">
                            <Label className="font-semibold">School Name</Label>
                            <Input
                                name="otherSchoolName"
                                placeholder="Enter School Name"
                                value={athleteLearning?.otherSchoolName}
                                onChange={(e) => handleOnChange('otherSchoolName', e.target.value)}
                            />
                        </div>}

                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">Graduation Year</Label>
                        <Select onValueChange={(option) => handleOnChange('graduationYear', option)}
                            value={athleteLearning?.graduationYear || ''}>
                            <SelectTrigger className="bg-white">
                                <SelectValue placeholder="Select Graduation Year" />
                            </SelectTrigger>
                            <SelectContent>
                                {allGraduationYears?.length > 0 ? allGraduationYears?.map(item =>
                                    <SelectItem value={item?.label} key={item?.label}>{item?.label}</SelectItem>
                                ) : <SelectItem disabled value="no-options">No Options Found</SelectItem>}
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">Grade</Label>
                        <Select onValueChange={(option) => handleOnChange('grade', option)}
                            value={athleteLearning?.grade || ''}>
                            <SelectTrigger className="bg-white">
                                <SelectValue placeholder="Select Grade" />
                            </SelectTrigger>
                            <SelectContent>
                                {gradesList?.map(item =>
                                    <SelectItem value={item} key={item}>{item}</SelectItem>
                                )}
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">Overall Academic Standing</Label>
                        <Select onValueChange={(option) => handleOnChange('overallAcademic', option)}
                            value={athleteLearning?.overallAcademic || ''}>
                            <SelectTrigger className="bg-white">
                                <SelectValue placeholder="Select..." />
                            </SelectTrigger>
                            <SelectContent>
                                {academicStandingsList?.length > 0 ? academicStandingsList?.map(item =>
                                    <SelectItem key={item} value={item}>{item}</SelectItem>
                                ) : <SelectItem disabled value="no-options">No Options Found</SelectItem>}
                            </SelectContent>
                        </Select>
                    </div>

                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">GPA</Label>
                        <NumericInput
                            placeholder="GPA (Enter numeric values)"
                            name='gpa'
                            value={athleteLearning?.gpa}
                            onChange={(e) => handleOnChange('gpa', e.target.value)}
                            thousandSeparator={false}
                        />
                    </div>

                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">SAT</Label>
                        <NumericInput
                            placeholder="SAT (Enter numeric values)"
                            name='sat'
                            value={athleteLearning?.sat}
                            onChange={(e) => handleOnChange('sat', e.target.value)}
                            thousandSeparator={false}
                        />
                    </div>

                    <div className="flex flex-col gap-1">
                        <Label className="font-semibold">ACT</Label>
                        <NumericInput
                            placeholder="ACT (Enter numeric values)"
                            name='act'
                            value={athleteLearning?.act}
                            onChange={(e) => handleOnChange('act', e.target.value)}
                            thousandSeparator={false}
                        />
                    </div>
                </div>

                <div className="flex flex-col gap-1 mt-5 w-full md:w-3/4">
                    <Label className="font-semibold text-wrap">Want to share more about your progress?</Label>
                    <div className="flex flex-col items-center justify-center">
                        <Textarea
                            className="border-slate-300 bg-white resize-none"
                            name='progress'
                            value={athleteLearning?.progress}
                            onChange={(e) => handleOnChange('progress', e.target.value)}
                            placeholder="Write here..."
                            maxLength={200}
                            rows={3}
                        />
                        <span className="text-destructive text-xs self-end text-end font-semibold">200 Char Max</span>
                    </div>
                </div>

                <Separator />

                <div className="flex flex-col gap-4 w-full">
                    <div className="flex flex-col items-center justify-center gap-1">
                        <h5 className="font-bold text-lg">Learning Strength Area</h5>
                        <span className="text-sm font-semibold text-center text-wrap">(Topics Athlete Enjoys)</span>
                    </div>
                    <div className="flex justify-center items-center gap-5 w-full">
                        <MultiSelectWithChip
                            options={allLearningTopicsList}
                            value={selectedLearingTopicsList}
                            name={'learningTopics'}
                            placeholder="Select Topics..."
                            onChange={(selected) => handleMultiSelectChange('selectedLearingTopicsList', selected)}
                            className="w-full md:w-3/4"
                        />
                    </div>
                </div>

                <Separator />

                <div className="flex flex-col gap-4 w-full md:w-3/4">
                    <h5 className="font-bold text-lg text-center">Proud Academic Moment</h5>
                    <div className="flex flex-col items-center justify-center">
                        <Textarea
                            maxLength={400}
                            placeholder="Write here..."
                            className="border-slate-300 w-full bg-white resize-none"
                            name='academicMmt'
                            value={athleteLearning?.academicMmt}
                            onChange={(e) => handleOnChange('academicMmt', e.target.value)}
                            rows={3}
                        />
                        <p className="text-destructive text-xs self-end font-semibold">400 Char Max</p>
                    </div>
                </div>

                <div className="flex items-center justify-end py-4 w-full">
                    <Button className="w-24" onClick={handleSave}>
                        {apiStatus === 'learingPending' ?
                            <Loader className="w-4 h-4 animate-spin" />
                            : 'Save'}
                    </Button>
                </div>
            </div >
        </>
    )
}
export default LearningHighLights