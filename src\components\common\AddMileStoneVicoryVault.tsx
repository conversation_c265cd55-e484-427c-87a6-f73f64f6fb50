'use client'
import CommonCalender from "@/components/common/CommonCalender"
import UploadFiles from "@/components/common/UploadFiles"
import { Button } from "@/components/ui/button"
import {
    Dialog,
    DialogContent,
    DialogTitle,
    DialogTrigger
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { AppDispatch, RootState } from "@/store"
import { fetchTags } from "@/store/slices/commonSlice"
import { EachMileStoneVictoryItem } from "@/utils/interfaces"
import { preventSpaces } from "@/utils/validations"
import { zodResolver } from '@hookform/resolvers/zod'
import { Loader, Plus } from "lucide-react"
import { useEffect } from "react"
import { Controller, useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { z } from 'zod'
import MultiSelectWithChip from "./MultiSelectWithChip"

interface IProps {
    origin: string
    open: boolean;
    handleAddModal: () => void;
    sourceData: Partial<EachMileStoneVictoryItem> | null;
    handleSaveForm: (data: EachMileStoneVictoryItem) => void;
    loading?: boolean;
    isEdit?: boolean;
    disable?: boolean
}


export const calendarSchema = z.discriminatedUnion("mode", [
    z.object({
        mode: z.literal("single"),
        date: z.date({ required_error: "Date is required" }),
    }),
    z.object({
        mode: z.literal("range"),
        date: z.object({
            from: z.date({ required_error: "Start date is required" }),
            to: z.date().optional(),
        }),
    }),
]);


const schema = z
    .object({
        date: z.date({
            required_error: "Date is required.",
        }),
        title: z
            .string()
            .min(1, 'Title is required'),
        blurb: z.any().optional(),
        link: z.any().optional(),
        tags: z.any().optional(),        
        file: z.any().optional()
    })

const AddMileStoneVicoryVault = ({
    origin,
    open,
    handleAddModal,
    sourceData,
    handleSaveForm,
    loading,
    isEdit,
    disable,
}: IProps) => {
    const { allTagsList } = useSelector((state: RootState) => state.commonSlice)
    const dispatch = useDispatch<AppDispatch>();
    type FormData = z.infer<typeof schema>;

    useEffect(() => {
        dispatch(fetchTags())
    }, [dispatch])

    const {
        control,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<FormData>({
        resolver: zodResolver(schema),
        defaultValues: {
            ...sourceData,
            title: sourceData?.title ?? '',
            link: sourceData?.link ?? '',
            blurb: sourceData?.blurb ?? '',
            tags: sourceData?.tags! ?? [],
            file: sourceData?.file ?? undefined,
            date: sourceData?.date ? new Date(sourceData?.date) : undefined,
        }
    });

    useEffect(() => {
        if (open && sourceData) {
            reset(sourceData);
        }
    }, [open, reset, sourceData]);

    useEffect(() => {
        if (open) {
            reset();
        }
    }, [open, reset]);

    const onSubmit = async (data: FormData) => {
        handleSaveForm(data as any)
        reset()
        handleAddModal()
    };

    const onError = (errors: any) => {
        console.error("Form validation errors:", errors);
    };

    return (
        <>
            <Dialog open={open} onOpenChange={handleAddModal}>
                <DialogTrigger asChild disabled={disable}>
                    <Button disabled={disable} onClick={handleAddModal} variant="outline" className="gap-2 font-semibold hover:text-primary">
                        <Plus />
                        Add {origin}
                    </Button>
                </DialogTrigger>
                <DialogContent
                    onInteractOutside={(event) => event.preventDefault()}
                    className='w-[70vw]  max-w-full max-h-[90%] m-0 p-0 flex flex-col'>
                    <DialogTitle className='text-xl font-bold p-4 border-b'>
                        {isEdit ? 'Update' : 'Add'} {origin}
                    </DialogTitle>
                    <div className='flex-grow flex flex-col overflow-y-scroll p-8'>
                        <form onSubmit={handleSubmit(onSubmit, onError)} >
                            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3  gap-4 ">
                                <div>
                                    <Controller
                                        name='date'
                                        control={control}
                                        render={({ field }) => (
                                            <CommonCalender
                                                placeholder={`${origin} Date`}
                                                mode='single'
                                                dateValue={field.value}
                                                setDateFn={(date) => field.onChange(date)}
                                                disabled={disable}
                                            />
                                        )}
                                    />
                                    {errors.date && (
                                        <p className='text-red-500 text-sm mt-1'>
                                            {errors.date.message}
                                        </p>
                                    )}
                                </div>

                                <div>
                                    <Controller
                                        name='title'
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                {...field}
                                                type={'text'}
                                                placeholder='Enter Title'
                                                onChange={(e) => {
                                                    const sanitizedValue = e.target.value
                                                        ?.trimStart()
                                                        ?.replace(preventSpaces, '');
                                                    field.onChange(sanitizedValue);
                                                }}
                                                className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                                disabled={disable}
                                            />
                                        )}
                                    />
                                    {errors.title && (
                                        <p className='text-red-500 text-sm mt-1'>
                                            {errors.title.message}
                                        </p>
                                    )}
                                </div>

                                <div>
                                    <Controller
                                        name='link'
                                        control={control}
                                        render={({ field }) => (
                                            <Input
                                                {...field}
                                                type={'text'}
                                                placeholder='Enter Link'
                                                onChange={(e) => {
                                                    const sanitizedValue = e.target.value
                                                        ?.trimStart()
                                                        ?.replace(preventSpaces, '');
                                                    field.onChange(sanitizedValue);
                                                }}
                                                disabled={disable}
                                                className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                            />
                                        )}
                                    />                                   
                                </div>

                                <div className="col-span-full">
                                    <Controller
                                        name='blurb'
                                        control={control}
                                        render={({ field }) => (
                                            <Textarea
                                                {...field}
                                                placeholder='Enter Blurb'
                                                onChange={(e) => {
                                                    const sanitizedValue = e.target.value
                                                        ?.trimStart()
                                                        ?.replace(preventSpaces, '');
                                                    field.onChange(sanitizedValue);
                                                }}
                                                disabled={disable}
                                                className='bg-white mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                            />
                                        )}
                                    />
                                    <div className="flex items-center justify-between">                                       
                                        {/* <p className="text-right text-destructive self-end text-xs">Max 200 chars</p> */}
                                    </div>
                                </div>

                                <div className="col-span-full">
                                    <Controller
                                        name="tags"
                                        control={control}
                                        render={({ field }) => {
                                            return (
                                                <MultiSelectWithChip
                                                    name="tags"
                                                    control={control}
                                                    options={allTagsList}
                                                    errors={errors}
                                                    placeholder="Select up to 3 tags..."
                                                    maxSelect={3}
                                                    disable={disable}
                                                />
                                            );
                                        }}
                                    />
                                </div>

                                <div className="col-span-full">
                                    <Controller
                                        name="file"
                                        control={control}
                                        render={({ field: { value, onChange } }) => (
                                            <UploadFiles
                                                acceptType={["image/*", "video/*", "application/pdf"]}
                                                value={value}
                                                onFileSelect={(data) => onChange(data)}
                                                handleRemove={() => onChange(null)}
                                                className=""
                                                disable={disable}
                                            />
                                        )}
                                    />
                                </div>
                            </div>

                            <div className='flex flex-row justify-end mt-4 gap-5'>
                                <Button disabled={disable} variant={'outline'} className='w-28 border-primary text-primary hover:text-primary' type='button' onClick={handleAddModal}>
                                    Cancel
                                </Button>
                                <Button disabled={disable || loading} className='w-28' type='submit'>
                                    {loading ? <Loader className="w-5 h-4 animate-spin" /> : 'Save'}
                                </Button>
                            </div>
                        </form>
                    </div>
                </DialogContent>
            </Dialog>
        </>
    )
}
export default AddMileStoneVicoryVault