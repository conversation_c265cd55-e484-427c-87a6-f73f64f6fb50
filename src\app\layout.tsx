import AuthorizedFooter from "@/components/AuthorizedFooter";
import AuthorizedNavBar from "@/components/AuthorizedNavbar";
import type { Metadata } from "next";
import { Ralew<PERSON> } from "next/font/google";
import NextTopLoader from 'nextjs-toploader';
import "swiper/css";
import "./globals.css";
import { Providers } from "./providers";

const raleway = Raleway({
  subsets: ["latin"],
  variable: "--font-raleway",
});

export const metadata: Metadata = {
  title: "Connect Athlete",
  description: "",
};

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  return (
    <html lang="en" className={raleway.variable}>
      <body className="font-sans antialiased bg-gray-50 flex flex-col min-h-screen">
        <Providers>
          <NextTopLoader
            color="#040761"
            showSpinner={false}
          />          
          <main className="flex-grow relative z-10">{children}</main>          
        </Providers>
      </body>
    </html>
  );
}
