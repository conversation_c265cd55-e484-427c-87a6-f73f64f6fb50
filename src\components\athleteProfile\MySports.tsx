'use client'
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON><PERSON>ooter,
    <PERSON><PERSON><PERSON>eader,
    DialogTitle,
    DialogTrigger
} from "@/components/ui/dialog"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { AppDispatch, RootState } from "@/store"
import { fetchAthleteAllSports } from "@/store/slices/athlete/athleteProfileSlice"
import { postAthleteSportInfo } from "@/store/slices/athlete/athleteSportProfileSlice"
import { currentSeasonStatusList, fetchAllSpecialities, fetchAllSportLevels, fetchAllSports } from "@/store/slices/commonSlice"
import { generateUpdateProfileUrl } from "@/utils/commonFunctions"
import { zodResolver } from "@hookform/resolvers/zod"
import { Loader, Plus } from "lucide-react"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { Controller, DefaultValues, useForm } from "react-hook-form"
import { useDispatch, useSelector } from "react-redux"
import { z } from "zod"
import MultiSelectWithChip from "../common/MultiSelectWithChip"
import SearchInput from "../common/SearchInput"
import SportItem from "../common/SportItem"
import { Input } from "../ui/input"
import { Label } from "../ui/label"
import { Skeleton } from "../ui/skeleton"
import { Switch } from "../ui/switch"

const sportFormSchema = z.object({
    selectedSport: z
        .object({
            value: z.number(),
            label: z.string(),
        })
        .nullable()
        .refine((val) => val !== null, {
            message: "Sport is required",
        }),
    isPrimarySport: z.boolean().optional(),
    yearsPlayed: z.string().optional(),
    currentSeason: z.any().optional(),
    selectedSportLevel: z.any().optional(),
    addedSportSpecilitiesList: z.any().optional(),
    currentTeam: z.string().optional(),
})

type SportFormValues = z.infer<typeof sportFormSchema>

const MySports = () => {
    const { mySportsList, apiStatus } = useSelector((state: RootState) => state.athleteProfile)
    const addApiStatus = useSelector((state: RootState) => state.athleteSportProfile.apiStatus)
    const { sportFormData } = useSelector((state: RootState) => state.athleteSportProfile)
    const { allSportsList, allSportLevelList, allSpecilitiesList } = useSelector((state: RootState) => state.commonSlice);
    const [addSportModal, setAddSportModal] = useState(false)
    const dispatch = useDispatch<AppDispatch>()
    const router = useRouter()
    const { profileData } = useLocalStoredInfo()
    const feProfileUrl = profileData ? generateUpdateProfileUrl(profileData) : ''

    useEffect(() => {
        dispatch(fetchAthleteAllSports())
    }, [dispatch])


    const {
        control,
        handleSubmit,
        watch,
        reset,
        formState: { errors }
    } = useForm<SportFormValues>({
        resolver: zodResolver(sportFormSchema),
        defaultValues: sportFormData as DefaultValues<SportFormValues>,
    })
    const selectedSportValue = watch("selectedSport")

    const initialFetches = async () => {
        await dispatch(fetchAllSports())
        await dispatch(fetchAllSportLevels())
    }

    useEffect(() => {
        initialFetches()
    }, [dispatch])

    useEffect(() => {
        selectedSportValue?.value && dispatch(fetchAllSpecialities(selectedSportValue.value))
    }, [selectedSportValue?.value, dispatch])

    useEffect(() => {
        reset({})
    }, [reset, router])

    const handleAddCancelSport = () => {
        setAddSportModal(!addSportModal)
        reset({})
    }

    const onSubmit = async (data: SportFormValues) => {
        const payload = {
            sportId: data?.selectedSport?.value,
            primarySport: data?.isPrimarySport || false,
            yearsPlayed: Number(data?.yearsPlayed || 0),
            currentTeam: data?.currentTeam,
            currentSeasonStatus: data?.currentSeason,
            levelId: data?.selectedSportLevel?.value,
            specialityIds: data?.addedSportSpecilitiesList?.map(each => each?.value)
        }

        try {
            const resultAction = await dispatch(postAthleteSportInfo(payload))
            if (postAthleteSportInfo.fulfilled.match(resultAction)) {
                const res = resultAction?.payload
                router.push(`${feProfileUrl}/${res?.sportName}/${res?.sportId}`)
                handleAddCancelSport()
            }
        } catch (error) {
            console.log(error)
        }
    }


    return (
        <div className="flex flex-col gap-4 bg-slate-100 p-5 rounded-lg h-full items-stretch">
            <div className="flex flex-col items-center justify-center gap-4">
                <div className="flex flex-col items-center justify-center">
                    <h3 className="font-bold text-xl text-center">My Sports/Activities</h3>
                    <span className="text-center text-sm text-secondary">(We recommend not more than 3 primary sports/activities)</span>
                </div>
                <div className="self-end">
                    <Dialog open={addSportModal} onOpenChange={handleAddCancelSport}>
                        <DialogTrigger asChild>
                            <Button variant="outline" className="gap-1 bg-slate-50 hover:bg-slate-50 font-semibold hover:text-blue-700">
                                <Plus />
                                Add Sport/Activity
                            </Button>
                        </DialogTrigger>
                        <DialogContent className="bg-white max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl" onInteractOutside={(event) => event.preventDefault()}>
                            <DialogHeader>
                                <DialogTitle>Add Sport/Activity</DialogTitle>
                            </DialogHeader>
                            <form onSubmit={handleSubmit(onSubmit)}>
                                <div className="flex flex-col gap-6 mt-5 min-h-[200px] max-h-[320px] xl:max-h-[400px] overflow-y-auto p-2">
                                    <div className="grid grid-cols-1 md:grid-cols-2 items-end gap-8 mb-3">
                                        <div className=" flex flex-col gap-1 w-full">
                                            <Label>Sport/Activity Name</Label>
                                            <Controller
                                                name="selectedSport"
                                                control={control}
                                                render={({ field }) => (
                                                    <SearchInput
                                                        {...field}
                                                        placeholder="Select Sport/Activity Name"
                                                        list={allSportsList}
                                                        value={field?.value || null}
                                                        onChange={(name, val) => field.onChange(val)}
                                                    />
                                                )}
                                            />
                                            {errors.selectedSport && <span className="text-red-600 text-sm">{errors.selectedSport.message}</span>}
                                        </div>
                                        <div className="flex flex-col gap-1">
                                            <Label>Primary Sport/Activity</Label>
                                            <Controller
                                                name="isPrimarySport"
                                                control={control}
                                                render={({ field }) => (
                                                    <Switch checked={field.value} onCheckedChange={field.onChange} />
                                                )}
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 md:grid-cols-2 gap-8 w-full">
                                        <div className="flex flex-col gap-1">
                                            <Label>Years Played</Label>
                                            <Controller
                                                name="yearsPlayed"
                                                control={control}
                                                render={({ field }) => (
                                                    <Input
                                                        {...field}
                                                        className="border-slate-300 bg-white"
                                                        placeholder="No. of years played"
                                                    />
                                                )}
                                            />
                                            {errors.yearsPlayed && <span className="text-red-600 text-sm">{errors.yearsPlayed.message}</span>}
                                        </div>

                                        <div className="flex flex-col gap-1">
                                            <Label>Current Season Status</Label>
                                            <Controller
                                                name="currentSeason"
                                                control={control}
                                                render={({ field }) => (
                                                    <Select onValueChange={(val) => field.onChange(val)}
                                                        value={field?.value || ''}>
                                                        <SelectTrigger className="bg-white">
                                                            <SelectValue placeholder="Select Season Status" />
                                                        </SelectTrigger>
                                                        <SelectContent>
                                                            {currentSeasonStatusList?.length > 0 ? currentSeasonStatusList?.map(item =>
                                                                <SelectItem value={item} key={item}>{item}</SelectItem>
                                                            ) : <SelectItem disabled value="no-options">No Options Found</SelectItem>}
                                                        </SelectContent>
                                                    </Select>
                                                )}
                                            />
                                        </div>
                                    </div>

                                    <div className="grid grid-cols-1 gap-8 w-full">
                                        <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-2">
                                            <Label className="col-span-1">Level</Label>
                                            <div className="flex flex-col col-span-3">
                                                <Controller
                                                    name="selectedSportLevel"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <SearchInput
                                                            {...field}
                                                            list={allSportLevelList}
                                                            value={field?.value}
                                                            placeholder="Select Sport/Activity Level"
                                                            onChange={(name, val) => field.onChange(val)}
                                                        />
                                                    )}
                                                />
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-4 items-start gap-2">
                                            <Label className="col-span-1 text-wrap mt-1">Position / Speciality</Label>
                                            <div className="flex flex-col col-span-3">
                                                <Controller
                                                    name="addedSportSpecilitiesList"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <MultiSelectWithChip
                                                            {...field}
                                                            options={allSpecilitiesList}
                                                            placeholder="Select Specialities..."
                                                            onChange={field.onChange}
                                                            value={field.value}
                                                            name="addedSportSpecilitiesList"
                                                        />
                                                    )}
                                                />
                                            </div>
                                        </div>

                                        <div className="grid grid-cols-1 md:grid-cols-4 items-center gap-2">
                                            <Label className="col-span-1">Current Team</Label>
                                            <div className="flex flex-col col-span-3">
                                                <Controller
                                                    name="currentTeam"
                                                    control={control}
                                                    render={({ field }) => (
                                                        <Input
                                                            {...field}
                                                            className="border-slate-300 text-primary placeholder-gray-800 bg-white"
                                                            placeholder="Current Team"
                                                        />
                                                    )}
                                                />
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <DialogFooter>
                                    <div className="flex items-center justify-end gap-3 mt-4">
                                        <Button variant="outline" type="button" onClick={handleAddCancelSport}>
                                            Cancel
                                        </Button>
                                        <Button type="submit">
                                            {addApiStatus === 'sportInfoPending' ? (
                                                <Loader className="mr-2 h-4 w-4 animate-spin" />
                                            ) : (
                                                'Save'
                                            )}
                                        </Button>
                                    </div>
                                </DialogFooter>
                            </form>
                        </DialogContent>
                    </Dialog>
                </div>
            </div>

            <div className="flex flex-col gap-5 py-5">
                {apiStatus === 'fetchAllSportsPending' ?
                    <>
                        {[1, 2, 3]?.map(each =>
                            <div key={each} className="flex items-center justify-between gap-6">
                                <Skeleton className="h-[20px] w-full rounded-lg bg-gray-300 animate-pulse" />
                                <div className="flex flex-col gap-2">
                                    <Skeleton className="h-[20px] w-full rounded-lg bg-gray-300 animate-pulse" />
                                    <Skeleton className="h-[20px] w-full rounded-lg bg-gray-300 animate-pulse" />
                                </div>
                                <Skeleton className="h-[20px] w-full rounded-lg bg-gray-300 animate-pulse" />
                            </div>)}
                    </>
                    :
                    <>
                        {mySportsList?.length > 0 ?
                            <div className="flex flex-col gap-5 max-h-[800px] overflow-y-auto scrollbar-hide">
                                {mySportsList?.map(each =>
                                    <SportItem key={each.id} item={each} />
                                )}
                            </div>
                            : <div className="flex items-center justify-center my-8">
                                <p className="text-gray-500 text-center text-wrap md:w-1/2">
                                    Your sports list is empty — start by adding one now and let’s get you in the game!
                                </p>
                            </div>}
                    </>}
            </div>
        </div >
    )
}
export default MySports