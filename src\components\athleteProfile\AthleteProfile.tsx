'use client'
import { useHandleAthleteSectionExpose } from "@/hooks/useAthleteExposeSections"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch, RootState } from "@/store"
import { fetchAthleteLocations, fetchAthleteOpenSession, fetchAthletePasscode, fetchAthleteProfileUrl, fetchAthleteSocialMediaLinks, fetchAthleteToggleSections, handleUpdateUserInput, putAthleteOpenSession } from "@/store/slices/athlete/athleteProfileSlice"
import { Option } from "@/utils/interfaces"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import PreferedLocations from "../common/PreferedLocations"
import ProfilesGallery from "../common/ProfilesGallery"
import ProfileUrl from "../common/ProfileUrl"
import QuickLinks from "../common/QuickLinks"
import ScrollToTop from "../common/ScrollToTop"
import SectionsLinkTracker from "../common/SectionsLinkTracker"
import SocialMedia from "../common/SocialMedia"
import { Label } from "../ui/label"
import { Switch } from "../ui/switch"
import AboutAthlete from "./AboutAthlete"
import Achievements from "./Achievements"
import GrowthDevelopment from "./GrowthDevelopment"
import LearningHighLights from "./LearningHighLights"
import MySports from "./MySports"
import ParentGuardianDetails from "./ParentGuardian"
import PhysicalStats from "./PhysicalStats"
import ProfileCard from "./ProfileCard"
import SchoolViewProfile from "./SchoolViewProfile"

const linkTrackList = [
    { id: 'athlete-aboutMe', img: '/about-me.png', label: 'About Me', },
    { id: 'athlete-mySports', img: '/my-sports.webp', label: 'My Sports/Activities', },
    { id: 'athlete-learning', img: '/learning.png', label: 'Learning', },
    { id: 'athlete-accomplisments', img: '/athlete-accomplisments.png', label: 'Accomplisments' },
    { id: 'athlete-phyStats', img: '/phy-stats.png', label: 'Phys Stats', },
    { id: 'athlete-location', img: '/location.svg', label: 'Location', },
    { id: 'athlete-aspirations', img: '/aspirations.png', label: 'Aspirations', },
    { id: 'athlete-contact', img: '/contact.png', label: 'Contact', },
];

const AthleteProfile = () => {
    const { athleteSocialMediaList, socialMediaToggle, athleteQuickLinksList,
        quickLinksToggle, openVirtualSession, isProfileSearchCA,
        selectedState, selectedCounties, selectedLocations, athleteAddedStateLocationsList,
        toggleGallery, athleteGalleryList, apiStatus, profileToggle, profileUrl, fetchedProfilePasscode,
    } = useSelector((state: RootState) => state.athleteProfile)
    const dispatch = useDispatch<AppDispatch>()
    const { handleToggleSections } = useHandleAthleteSectionExpose()
    const { isPremiumUser, roleId, userId } = useTokenValues()

    const initialFetches = async () => {
        await dispatch(fetchAthleteSocialMediaLinks())
        await dispatch(fetchAthleteOpenSession())
        await dispatch(fetchAthleteProfileUrl())
        await dispatch(fetchAthleteToggleSections())
        await dispatch(fetchAthleteLocations())
        await dispatch(fetchAthletePasscode())
    }

    useEffect(() => {
        initialFetches()
    }, [dispatch])

    const handleStateLocations = (name: string, value: Option | Option[]) => {
        if (name === 'selectedState') {
            dispatch(handleUpdateUserInput({ name, value }))
            dispatch(handleUpdateUserInput({ name: 'selectedLocations', value: [] }))
            dispatch(handleUpdateUserInput({ name: 'selectedCounties', value: [] }))
        } else if (name === 'selectedLocations') {
            dispatch(handleUpdateUserInput({ name, value }))
        } else if (name === 'selectedCounties') {
            dispatch(handleUpdateUserInput({ name, value }))
        }
    }


    const handleOpenVirtualSession = async (checked) => {
        try {
            const payload = {
                openToVirtualSession: checked ? "Y" : "N"
            }
            const resultAction = await dispatch(putAthleteOpenSession(payload))
            if (putAthleteOpenSession.fulfilled.match(resultAction)) {
                await dispatch(fetchAthleteOpenSession())
            }
        } catch (error) {
            console.error(error)
        }
    }

    return (
        <>
            <div className="flex flex-col flex-grow w-full">
                <div className="flex flex-col gap-11">
                    {/* <h1 className="text-green-600 font-semibold text-center">Please Verify Your Email in Contact section Below For Free User Access</h1> */}
                    <ProfileUrl
                        fetchedProfileUrl={profileUrl}
                        toggleProfile={profileToggle}
                        fetchedProfilePasscode={fetchedProfilePasscode}
                        loading={apiStatus === 'passcodePending'}
                        handleToggleProfile={(checked) => handleToggleSections('profile', checked)}
                    />

                    <SectionsLinkTracker linkTrackList={linkTrackList} />

                    <ProfileCard />

                    <div id='athlete-aboutMe'>
                        <AboutAthlete />
                    </div>

                    <div id='athlete-learning'>
                        <LearningHighLights />
                    </div>

                    <div id='athlete-phyStats'>
                        <PhysicalStats />
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-6 gap-10 items-stretch h-full">
                        <div className="flex flex-col gap-6 lg:col-span-3 items-stretch h-full">
                            <div id='athlete-mySports' className="h-full items-stretch">
                                <MySports />
                            </div>
                        </div>

                        <div className="lg:col-span-3 h-full max-h-full items-stretch">
                            <div id='athlete-location'>
                                <div className="flex items-center justify-center gap-4 bg-slate-100 rounded-lg p-4">
                                    <Label htmlFor="openVirtualSession" className="font-bold text-xl">Open To Virtual Sessions</Label>
                                    <Switch
                                        name='openVirtualSession'
                                        id={'openVirtualSession'}
                                        checked={openVirtualSession}
                                        onCheckedChange={handleOpenVirtualSession}
                                    />
                                </div>
                                <PreferedLocations
                                    selectedState={selectedState}
                                    selectedLocations={selectedLocations}
                                    selectedCounties={selectedCounties}
                                    loading={apiStatus === 'locationPending'}
                                    handleStateLocations={handleStateLocations}
                                    addedStateLocationsList={athleteAddedStateLocationsList}
                                />
                            </div>
                        </div>
                    </div>

                    <div className="grid grid-cols-1 lg:grid-cols-5 gap-10 items-stretch h-full">
                        <div className="flex flex-col gap-6 lg:col-span-3 h-full">
                            <SocialMedia
                                origin='athlete'
                                list={athleteSocialMediaList}
                                fetchLoading={apiStatus === 'fetchAthleteSocialMPending'}
                                loading={apiStatus === 'socialMediaPending'}
                                toggleSocialMediaSection={socialMediaToggle}
                                onChangeToggleSection={(checked) => handleToggleSections('socialMedia', checked)}
                            />
                            <QuickLinks
                                origin='athlete'
                                fetchLoading={apiStatus === 'fetchLinkPending'}
                                loading={apiStatus === 'quickLinksPending'}
                                list={athleteQuickLinksList}
                                toggleQuickLinkSection={quickLinksToggle}
                                onChangeToggleSection={(checked) => handleToggleSections('quickLinks', checked)}
                            />
                        </div>

                        <div className="lg:col-span-2 h-full max-h-full">
                            <ProfilesGallery
                                toggleGallery={toggleGallery}
                                handleToggleSection={(name, checked) => handleToggleSections('gallery', checked)}
                                list={athleteGalleryList}
                                fetchLoading={apiStatus === 'fetchGalleryPending'}
                                loading={apiStatus === 'galleryPending'}
                                origin="Athlete"
                            />
                        </div>
                    </div>

                    <div id='athlete-accomplisments'>
                        <Achievements />
                    </div>

                    <div id='athlete-aspiration'>
                        <GrowthDevelopment />
                    </div>

                    <div id='athlete-contact'>
                        <ParentGuardianDetails />
                    </div>

                    <div className="bg-slate-100 rounded-lg p-4 flex flex-col gap-5">
                        <div className="flex items-center justify-center gap-2">
                            <h3 className="text-xl font-bold text-wrap text-center">
                                Search My Profile on CA</h3>
                            <Switch
                                checked={isProfileSearchCA}
                                onCheckedChange={(checked) => handleToggleSections('isProfileSearchCA', checked)}
                            />
                        </div>
                        <span className="text-lg text-secondary text-center">
                            Your profile will be visible to Coaches , Sports
                            Businesses/Orgs in Connect Athlete. If this
                            is disabled , then your profile will not show
                            up on the Search Assistant.
                        </span>
                    </div>

                    <div>
                        <SchoolViewProfile />
                    </div>

                    <div className="flex flex-col gap-2 rounded-lg bg-slate-100 p-3 space-y-4 mb-8 lg:mb-0">
                        <span className="text-orange-500">Note:</span>
                        <ul className="flex flex-col gap-2">
                            <li>
                                <Switch checked className="cursor-not-allowed" /> On (enabled) → The feature will be published and visible to others.
                            </li>
                            <li>
                                <Switch checked={false} className="cursor-not-allowed" /> Off (disabled) → The feature will be hidden and not visible publicly.
                            </li>
                        </ul>
                    </div>

                    <ScrollToTop />
                </div >
            </div>
        </>
    )
}
export default AthleteProfile