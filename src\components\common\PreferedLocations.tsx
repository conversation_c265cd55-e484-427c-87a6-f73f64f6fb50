
'use client'
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch, RootState } from "@/store"
import { deleteAthleteLocations, fetchAthleteLocations, postAthleteLocations } from "@/store/slices/athlete/athleteProfileSlice"
import { fetchAllCounties, fetchAllLocations, fetchAllStates, handleCommonSliceUpdateStates } from "@/store/slices/commonSlice"
import { ROLES } from "@/utils/constants"
import { AddedStateLocationsItem, Option } from "@/utils/interfaces"
import { Loader } from "lucide-react"
import { useEffect } from "react"
import { useDispatch, useSelector } from "react-redux"
import Select from "react-select"
import { Button } from "../ui/button"
import { Label } from "../ui/label"
import { Separator } from "../ui/separator"
import { Skeleton } from "../ui/skeleton"
import StateLocationItem from "./StateLocationItem"

const customStyles = {
    control: (base, state) => ({
        ...base,
        backgroundColor: 'white',
        borderColor: state.isFocused ? '#04076140' : '',
        boxShadow: state.isFocused ? '0 0 0 2px rgba(79, 70, 229, 0.3)' : 'none',
        '&:hover': {
            borderColor: '#04076140',
        },
        minHeight: '40px',
        fontSize: '14px',
        borderRadius: '8px'
    }),
    menu: (base) => ({
        ...base,
        backgroundColor: '#ffffff',
        zIndex: 9999,
    }),
    option: (base, state) => ({
        ...base,
        backgroundColor: state.isSelected
            ? '#4F46E5'
            : state.isFocused
                ? '#E0E7FF'
                : 'transparent',
        color: state.isSelected ? '#ffffff' : '#1F2937',
        padding: '10px 12px',
        fontSize: '14px',
    }),
    singleValue: (base) => ({
        ...base,
        color: '#1F2937',
        fontWeight: 500,
    }),
    placeholder: (base) => ({
        ...base,
        color: '#9CA3AF',
        fontSize: '14px',
    }),
    indicatorSeparator: () => ({
        display: 'none',
    }),
    dropdownIndicator: (base) => ({
        ...base,
        color: '#04076160',
        fontSize: '10px',
    }),
    clearIndicator: (base) => ({
        ...base,
        color: '#04076170',
        '&:hover': {
            color: '#040761',
        },
    }),
    multiValue: (styles: any, { data }: any) => {
        return {
            ...styles,
            backgroundColor:
                data.value === 0 ? "#fde68a" : "#f08424",
            borderRadius: "9999px",
            paddingLeft: "6px",
            paddingRight: "6px",
        };
    },
    multiValueLabel: (styles: any, { data }: any) => ({
        ...styles,
        color: data.value === 0 ? "#92400e" : "white",
        fontWeight: 500,
    }),
    multiValueRemove: (styles: any, { data }: any) => ({
        ...styles,
        color: data.value === 0 ? "#92400e" : "white",
        ':hover': {
            color: data.value === 0 ? "#92400e" : "white",
            borderRadius: "9999px",
        }
    }),
};


interface IProps {
    selectedState: Option | null;
    selectedCounties: Option[];
    selectedLocations: Option[]
    loading?: boolean;
    fetchLoading?: boolean;
    handleStateLocations: (name: string, value: any) => void;
    addedStateLocationsList: AddedStateLocationsItem[];
    handleUpdateAddedStateLocations?: (list: AddedStateLocationsItem[]) => void
}

const PreferedLocations = ({
    selectedState,
    selectedCounties,
    selectedLocations,
    loading,
    fetchLoading,
    handleStateLocations,
    addedStateLocationsList,
}: IProps) => {
    const { allStatesList, allLocationsList, allCountiesList } = useSelector((state: RootState) => state.commonSlice)
    const dispatch = useDispatch<AppDispatch>()
    const { roleId, userId } = useTokenValues()
    const { profileId } = useLocalStoredInfo()

    useEffect(() => {
        dispatch(fetchAllStates())
    }, [dispatch])

    useEffect(() => {
        if (selectedState?.value) {
            dispatch(fetchAllCounties(selectedState?.value))
            dispatch(fetchAllLocations(selectedState?.value))
        } else {
            dispatch(handleCommonSliceUpdateStates({ name: 'allCountiesList', value: [] }))
            dispatch(handleCommonSliceUpdateStates({ name: 'allLocationsList', value: [] }))
        }
    }, [selectedState?.value, dispatch])

    const clearStateLocation = () => {
        handleStateLocations('selectedState', null)
        handleStateLocations('selectedCounties', [])
        handleStateLocations('selectedLocations', [])
    }

    const handleAddAthleteLocations = async () => {
        const isOnlyStateSelected = selectedState?.value && selectedCounties?.length <= 0 && selectedLocations?.length <= 0

        const payload = {
            athleteId: profileId,
            userId,
            roleId,
            stateId: selectedState?.value,
            selectedCountyIds: selectedCounties?.length > 0 ? selectedCounties?.map(each => each.value) : [],
            selectedCityIds: selectedLocations?.length > 0 ? selectedLocations?.map(each => each?.value) : [],
            selectAllCities: Boolean(isOnlyStateSelected)
        }

        try {
            const result = await dispatch(postAthleteLocations([payload]))
            if (postAthleteLocations.fulfilled.match(result)) {
                clearStateLocation()
                await dispatch(fetchAthleteLocations())
            }
        } catch (error) {
            console.log(error)
        }
    }

    const handleAddCoachLocations = async () => {
        //Coach Locations
    }

    const handleAddLocations = async () => {
        if (!selectedState?.value) return;

        switch (roleId) {
            case ROLES.ATHLETE:
                handleAddAthleteLocations()
                return;
            case ROLES.COACH:
                handleAddCoachLocations()
                return;
            default:
                return;
        }
    };

    const handleDeleteAthleteLocations = async (stateId: number) => {
        try {
            const result = await dispatch(deleteAthleteLocations(stateId))
            if (deleteAthleteLocations.fulfilled.match(result)) {
                await dispatch(fetchAthleteLocations())
            }
        } catch (error) {
            console.log(error)
        }
    }

    const handleDeleteCoachLocations = async (stateId: number) => {
        //Coach delete locations
    }

    const handleDeleteStateLocations = async (stateId: number) => {
        switch (roleId) {
            case ROLES.ATHLETE:
                handleDeleteAthleteLocations(stateId)
                return;
            case ROLES.COACH:
                handleDeleteCoachLocations(stateId)
                return;
            default:
                return;
        }
    };

    return (
        <>
            <div className="w-full flex flex-col gap-5 bg-slate-100 p-4 rounded-lg">
                <div className="flex flex-col items-center justify-center gap-4">
                    <h3 className="font-bold text-xl text-center">Preferred Locations For In-Person Sessions</h3>

                </div>
                <div className="flex flex-col gap-5">
                    <div className="grid grid-cols-1 flex-col justify-center items-center gap-5">
                        <div className="flex flex-col gap-1">
                            <Label>Selecting the state alone includes all locations within it. Choosing cities or counties will select only those specific areas.</Label>
                            <Select
                                name="selectedState"
                                options={allStatesList}
                                value={selectedState}
                                onChange={(selectedOption) => { handleStateLocations('selectedState', selectedOption) }}
                                isClearable
                                placeholder='Select State...'
                                className="w-full"
                                styles={customStyles}
                            />
                        </div>

                        <div className="flex flex-col gap-1">
                            <Label>Select one or more counties from the list</Label>
                            <Select
                                name="selectedCounties"
                                options={allCountiesList}
                                value={selectedCounties}
                                onChange={(selectedOptions) => { handleStateLocations('selectedCounties', selectedOptions) }}
                                isClearable
                                placeholder='Select Counties...'
                                className="w-full border-slate-300"
                                styles={customStyles}
                                isMulti
                            />
                        </div>

                        <div className="flex flex-col gap-1">
                            <Label className="leading-5">Select one or more cities.                               </Label>
                            <Select
                                name="selectedLocations"
                                placeholder='Select Locations...'
                                options={allLocationsList}
                                isMulti
                                value={selectedLocations}
                                // onChange={(selectedOption) => {
                                //     let selected =
                                //         (selectedOption as {
                                //             value: number;
                                //             label: string;
                                //         }[]) || [];

                                //     if (selected.some((city) => city.value === 0)) {
                                //         selected = [{ value: 0, label: "All Cities" }];
                                //         handleStateLocations('selectedCounties', [])
                                //     } else {
                                //         selected = selected.filter(
                                //             (city) => city.value !== 0
                                //         );
                                //     }
                                //     handleStateLocations('selectedLocations', selected)
                                // }}
                                onChange={(selectedOptions) => { handleStateLocations('selectedLocations', selectedOptions) }}
                                styles={customStyles}
                            />
                        </div>
                    </div>
                    <Button className="self-center bg-primary hover:bg-blue-900 min-w-24"
                        onClick={handleAddLocations} disabled={loading || !selectedState?.value}>
                        {loading ? <Loader className='w-5 h-5 animate-spin' /> : "Add Locations"}
                    </Button>
                </div>

                <Separator />

                <div className="flex flex-col items-center justify-center gap-5">
                    {fetchLoading ?
                        <div className="flex items-center gap-2">
                            <div>
                                <Skeleton className="h-[20px] w-full rounded-lg bg-gray-200 animate-pulse" />
                                <div className="flex flex-col">
                                    <Skeleton className="h-[20px] w-full rounded-lg bg-gray-200 animate-pulse" />
                                    <Skeleton className="h-[20px] w-full rounded-lg bg-gray-200 animate-pulse" />
                                </div>
                            </div>
                            <div className="flex justify-end gap-2 w-full">
                                <Skeleton className="h-5 w-24 bg-gray-200 " />
                                <Skeleton className="h-5 w-24 bg-gray-200 " />
                            </div>
                        </div>
                        :
                        addedStateLocationsList?.length > 0 ? (
                            <>
                                <h3 className="font-semibold text-lg">Saved Location Preferences:</h3>
                                <ul className="flex gap-3 flex-col w-full items-center mb-4 max-h-[800px] overflow-y-auto scrollbar-hide">
                                    {addedStateLocationsList?.map((each, index) => {
                                        return (
                                            <StateLocationItem
                                                item={each}
                                                key={each?.stateId}
                                                handleDelete={handleDeleteStateLocations}
                                            />
                                        );
                                    })}
                                </ul>
                            </>
                        ) :
                            <div className="flex items-center justify-center my-3">
                                <p className="text-gray-500 text-center">
                                    Your preferred locations list is empty
                                </p>
                            </div>
                    }
                </div>

            </div>
        </>
    )
}
export default PreferedLocations