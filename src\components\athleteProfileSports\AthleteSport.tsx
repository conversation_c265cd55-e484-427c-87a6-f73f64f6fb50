'use client'
import { useHandleAthleteSectionExpose } from "@/hooks/useAthleteExposeSections"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch, RootState } from "@/store"
import { fetchAthleteProfileUrl } from "@/store/slices/athlete/athleteProfileSlice"
import { deleteAthleteSport, deleteAthleteSportVideos, fetchAthleteSportLinks, fetchAthleteSportProfileHide, fetchAthleteSportSectionsHide, fetchAthleteSportVideos, handleUpdateUserInput, postAthleteSportVideos, putAthleteSportProfileHide, putAthleteSportVideos } from "@/store/slices/athlete/athleteSportProfileSlice"
import { CheckCheck, Copy, Trash2 } from "lucide-react"
import { Params } from "next/dist/shared/lib/router/utils/route-matcher"
import { useRouter } from "next/navigation"
import { useEffect, useState } from "react"
import { useDispatch, useSelector } from "react-redux"
import AlertPopup from "../common/AlertPopup"
import BackButton from "../common/BackButton"
import ScrollToTop from "../common/ScrollToTop"
import SportHighLightLinks from "../common/SportHighLightLinks"
import SportHighLightVideos from "../common/SportHighLightVideos"
import { Button } from "../ui/button"
import { Switch } from "../ui/switch"
import SportCard from "./SportCard"
import SportMileStone from "./SportMileStone"
import SportStats from "./SportStats"
import VictoryVault from "./VictoryVault"


interface IProps {
    params: Params
}
const AthleteSport = ({ params }: IProps) => {
    const { profileUrl } = useSelector((state: RootState) => state.athleteProfile)
    const { videoModalOpen, videoId, toggleVideoSection, latestVideoData, addedHighlightVideoList,
        highlightLinksList, toggleHighlightLinks, isEditHighlightLinks, apiStatus, isSportProfileHide } = useSelector((state: RootState) => state.athleteSportProfile)
    const decodedSportName = decodeURIComponent(params?.sportName);
    const { handleAthleteSportsSectionsHide } = useHandleAthleteSectionExpose()
    const { roleId, userId } = useTokenValues()
    const { profileId, userInfo } = useLocalStoredInfo()
    const [coppied, setCoppied] = useState(false);
    const editedUrl = `${process.env.NEXT_PUBLIC_CONNECT_ATHLETE_URL}${profileUrl}/${decodedSportName}`
    const profileCompleteUrl = `${process.env.NEXT_PUBLIC_CONNECT_ATHLETE_URL}${userInfo?.profileUrl}/${decodedSportName}`
    const dispatch = useDispatch<AppDispatch>()
    const router = useRouter()

    const initialFetches = async () => {
        await dispatch(fetchAthleteSportLinks(params?.sportId))
        await dispatch(fetchAthleteProfileUrl())
        await dispatch(fetchAthleteSportVideos(params?.sportId))
        await dispatch(fetchAthleteSportProfileHide(params?.sportId))
        await dispatch(fetchAthleteSportSectionsHide(params?.sportId))
    }

    useEffect(() => {
        initialFetches()
    }, [dispatch])

    const handleCopyUrl = () => {
        navigator.clipboard
            .writeText(editedUrl || profileCompleteUrl)
            .then(() => {
                setCoppied(true);
                setTimeout(() => {
                    setCoppied(false);
                }, 1000);
            })
            .catch((err) => console.error('Failed to copy: ', err));
    };

    const handleOnChange = (name: string, value: string | boolean) => {
        if (name === "toggleHighlightLinks") {
            handleAthleteSportsSectionsHide(params?.sportId, name, value as boolean)
        } else {
            dispatch(handleUpdateUserInput({ name, value }))
        }

    }

    const handleUpdateLatestVideoValues = (name, value) => {
        if (name === 'toggleVideoSection') {
            handleAthleteSportsSectionsHide(params?.sportId, name, value)
        } else {
            dispatch(handleUpdateUserInput({ name: 'latestVideoData', value: { ...latestVideoData, [name]: value } }));
        }
    }

    const videoAPISuccessStatus = async () => {
        await dispatch(fetchAthleteSportVideos(params?.sportId))
        dispatch(handleUpdateUserInput({ name: 'latestVideoData', value: null }));
        dispatch(handleUpdateUserInput({ name: 'videoModalOpen', value: false }));
        dispatch(handleUpdateUserInput({ name: 'videoId', value: null }));
    }

    const handleSaveHighlightVideo = async () => {
        const payload = {
            roleId,
            athleteId: profileId,
            userId,
            sportId: Number(params?.sportId),
            videotitle: latestVideoData?.title,
            aboutVideo: latestVideoData?.aboutVideo,
            videoUrl: latestVideoData?.video,
            isHidden: false,
            isDeleted: false
        }
        try {
            if (videoId) {
                const resultAction = await dispatch(putAthleteSportVideos({ payload, videoId }))
                if (putAthleteSportVideos.fulfilled.match(resultAction)) {
                    videoAPISuccessStatus()
                }
            } else {
                const resultAction = await dispatch(postAthleteSportVideos(payload))
                if (postAthleteSportVideos.fulfilled.match(resultAction)) {
                    videoAPISuccessStatus()
                }
            }
        } catch (error) {
            console.log(error)
        }
    }

    const handleEditHighLightVideo = (id: number) => {
        dispatch(handleUpdateUserInput({ name: 'videoId', value: id }))
        const pickedVideo = addedHighlightVideoList?.find(each => each?.id === id)
        dispatch(handleUpdateUserInput({ name: 'latestVideoData', value: pickedVideo }));
        dispatch(handleUpdateUserInput({ name: 'videoModalOpen', value: true }));
    }

    const handleDeleteHighLightVideo = async (id: number) => {
        try {
            const resultAction = await dispatch(deleteAthleteSportVideos(id))
            if (deleteAthleteSportVideos.fulfilled.match(resultAction)) {
                await dispatch(fetchAthleteSportVideos(params?.sportId))
            }
        } catch (error) {
            console.log(error)
        }
    }

    const handleModal = () => {
        dispatch(handleUpdateUserInput({ name: 'videoModalOpen', value: !videoModalOpen }));
        dispatch(handleUpdateUserInput({ name: 'latestVideoData', value: null }));
    }

    const handleSportProfileHide = async (checked) => {
        const payload = {
            roleId,
            athleteId: profileId,
            sportsProfileUrl: '',
            hideSportsProfileUrl: !checked,
            pinCode: ""
        }

        try {
            const resultAction = await dispatch(putAthleteSportProfileHide({ payload, sportId: params?.sportId }))
            if (putAthleteSportProfileHide.fulfilled.match(resultAction)) {
                await dispatch(fetchAthleteSportProfileHide(params?.sportId))
            }
        } catch (error) {
            console.log(error)
        }
    }

    const handleDeleteSport = async () => {
        try {
            const resultAction = await dispatch(deleteAthleteSport(params?.sportId))
            if (deleteAthleteSport.fulfilled.match(resultAction)) {
                router.back()
            }
        } catch (error) {
            console.log(error)
        }
    }

    return (
        <>
            <div className="flex flex-col flex-grow w-full">
                <div className="flex flex-col gap-8 py-10">
                    <BackButton />

                    <div className="flex flex-col gap-2 items-center justify-center flex-wrap">
                        <h3 className="text-xl font-bold">{decodedSportName ? ` ${decodedSportName}` : `Sport`} Profile </h3>
                        <div className="flex flex-col md:flex-row items-center justify-center flex-wrap gap-3 w-full">
                            <a
                                href={`${editedUrl}` || profileCompleteUrl}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="w-full md:w-auto break-words text-center text-wrap font-semibold text-xl hover:text-blue-600 hover:underline"
                            >
                                {profileUrl ? `${editedUrl}` : profileCompleteUrl}
                            </a>

                            {coppied ? (
                                <CheckCheck
                                    className='cursor-pointer text-gray-800'
                                    size={14}
                                />
                            ) : (
                                <Copy
                                    className='cursor-pointer text-gray-800'
                                    size={14}
                                    onClick={handleCopyUrl}
                                />
                            )}
                        </div>
                        <div className="flex items-center gap-5">
                            <Switch onCheckedChange={handleSportProfileHide} checked={isSportProfileHide} />
                            <AlertPopup
                                trigger={<Button variant="destructive" size="icon"><Trash2 /></Button>}
                                alertTitle="Delete Confirmation"
                                alertContent="Are you sure, you want to delete this sport?"
                                action={handleDeleteSport}
                            />
                        </div>
                    </div>

                    <SportCard params={params} />


                    <SportStats sportName={decodedSportName} sportId={params?.sportId} />

                    <SportHighLightVideos
                        params={params}
                        loading={apiStatus === 'sportVideoPending'}
                        toggleVideoSection={toggleVideoSection}
                        latestVideoData={latestVideoData}
                        addedHighLightVideoList={addedHighlightVideoList}
                        handleUpdateLatestVideoValues={handleUpdateLatestVideoValues}
                        handleSaveHighlightVideo={handleSaveHighlightVideo}
                        handleEditHighLightVideo={handleEditHighLightVideo}
                        handleDeleteHighLightVideo={handleDeleteHighLightVideo}
                        openModal={videoModalOpen}
                        handleModal={handleModal}
                        isEdit={Boolean(videoId)}
                    />

                    <SportHighLightLinks
                        toggleHighlightLinks={toggleHighlightLinks}
                        highlightLinksList={highlightLinksList}
                        isEditHighlightLinks={isEditHighlightLinks}
                        handleOnChange={handleOnChange}
                        params={params}
                        loading={apiStatus === 'athleteSportLinkPending'}
                    />

                    <SportMileStone params={params} />

                    <VictoryVault params={params} />

                    <ScrollToTop />
                </div>
            </div>
        </>
    )
}
export default AthleteSport