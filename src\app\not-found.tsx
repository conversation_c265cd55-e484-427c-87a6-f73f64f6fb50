'use client'

import PublicNavBar from "@/components/common/PublicNavBar";
import { Button } from "@/components/ui/button";
import { useTokenValues } from "@/hooks/useTokenValues";
import { ROLES } from "@/utils/constants";
import { ArrowLeft } from "lucide-react";
import Link from "next/link";
import { useMemo } from "react";

const RouteNotFound = () => {
    const { roleId } = useTokenValues();

    const roleHomePath = useMemo(() => {
        switch (roleId) {
            case ROLES.ATHLETE:
                return '/athlete';
            case ROLES.COACH:
                return '/coach';
            case ROLES.BUSINESS:
                return '/business';
            default:
                return '/';
        }
    }, [roleId]);

    return (
        <>
            <PublicNavBar />
            <div className="flex flex-col items-center justify-center h-screen-minus-header px-4 text-center dark:bg-gray-900">
                <img
                    src="/404.png"
                    alt="Page not found"
                    className="w-full max-w-md mb-8"
                />
                <h2 className="text-3xl font-semibold text-gray-700 dark:text-gray-300 mb-2">
                    Page Not Found
                </h2>
                <p className="text-gray-500 dark:text-gray-400 mb-6 text-wrap text-center w-[50%]">
                    The page you requested is not available. It may have been moved, had its name changed, or is temporarily inaccessible.
                </p>

                <Button asChild variant="default" className="gap-2">
                    <Link href={roleHomePath}>
                        <ArrowLeft className="h-4 w-4" />
                        Go Home
                    </Link>
                </Button>
            </div>
            <div className="pt-4 w-full flex items-center justify-center p-4 mt-5">
                <p className="text-center max-w-7xl lg:px-14 font-semibold">
                    © 2025 Connect Athlete. All rights reserved.
                    Connect Athlete™, SPARQX™, SAAR™, and all related technologies are the
                    intellectual property of Connect Athlete and protected by copyright
                    and trademark laws.
                </p>
            </div>
        </>
    );
};

export default RouteNotFound;
