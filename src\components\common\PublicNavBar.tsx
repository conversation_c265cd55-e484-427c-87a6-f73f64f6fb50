'use client'

const PublicNavBar = () => {

    return (
        <>
            <div className="mb-[4.5rem]">
                <nav className="h-15 p-1 fixed top-0 left-0 right-0 w-full z-50  bg-primary shadow-md">
                    <div className="max-w-7xl mx-auto px-5 lg:px-8 flex justify-between items-center ">
                        <a href={process.env.NEXT_PUBLIC_CONNECT_ATHLETE_URL}
                            target="_blanck"
                            rel="noopener noreferrer"
                        >
                            <img src='/connectathlete-logo.svg' alt="Connect Athlete" className="cursor-pointer" />
                        </a>
                        <div></div>
                    </div>
                </nav>
            </div>
        </>
    )
}
export default PublicNavBar