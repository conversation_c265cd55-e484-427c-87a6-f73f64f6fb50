'use client'
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON><PERSON><PERSON>er,
    <PERSON><PERSON><PERSON>eader,
    <PERSON><PERSON><PERSON><PERSON><PERSON>,
    DialogTrigger
} from "@/components/ui/dialog"
import {
    Toolt<PERSON>,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
} from "@/components/ui/tooltip"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch } from "@/store"
import { fetchAthletePasscode, fetchAthleteProfileUrl, handleUpdateUserInput, postAthleteProfilePasscode } from "@/store/slices/athlete/athleteProfileSlice"
import { putProfileUrl } from "@/store/slices/commonSlice"
import { ROLES } from "@/utils/constants"
import { preventAllSpaces, preventSpaces } from "@/utils/validations"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>O<PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash2 } from "lucide-react"
import { <PERSON><PERSON><PERSON>, useEffect, useState } from "react"
import { useDispatch } from "react-redux"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Switch } from "../ui/switch"

interface IProps {
    toggleProfile?: boolean;
    handleToggleProfile?: (checked: boolean) => void;
    fetchedProfileUrl?: string;
    fetchedProfilePasscode?: string;
    loading?: boolean;
}

const ProfileUrl = ({ toggleProfile, fetchedProfileUrl, handleToggleProfile, fetchedProfilePasscode, loading, }: IProps) => {
    const { userInfo } = useLocalStoredInfo()
    const { userId, roleType, roleId } = useTokenValues()
    const [coppied, setCoppied] = useState(false);
    const [profileUrl, setProfileUrl] = useState(userInfo?.profileUrl);
    const [preferredName, setPreferredName] = useState('')
    const [passcode, setPasscode] = useState('')
    const [openModal, setOpenModal] = useState(false);
    const [showPasscode, setShowPasscode] = useState(false);
    const [openPasscodeModal, setOpenPasscodeModal] = useState(false);
    const dispatch = useDispatch<AppDispatch>()

    const handleCopyUrl = () => {
        navigator.clipboard
            .writeText(`${process.env.NEXT_PUBLIC_CONNECT_ATHLETE_URL}${profileUrl}`)
            .then(() => {
                setCoppied(true);
                setTimeout(() => {
                    setCoppied(false);
                }, 1000);
            })
            .catch((err) => console.error('Failed to copy: ', err));
    };

    useEffect(() => {
        userInfo?.profileUrl && setProfileUrl(userInfo?.profileUrl)
    }, [userInfo])

    useEffect(() => {
        fetchedProfileUrl && setProfileUrl(fetchedProfileUrl)
    }, [fetchedProfileUrl])


    useEffect(() => {
        if (!openModal) {
            setPreferredName('')
        }
    }, [openModal])

    useEffect(() => {
        if (!openPasscodeModal) {
            setPasscode('')
        }
    }, [openPasscodeModal])

    const handleChangePasscode = (event: ChangeEvent<HTMLInputElement>) => {
        const sanitizedValue = event.target.value?.trimStart()?.replace(preventAllSpaces, '');
        switch (roleId) {
            case ROLES.ATHLETE:
                setPasscode(sanitizedValue)
                dispatch(handleUpdateUserInput({ profilePasscode: sanitizedValue }))
                return;
            case ROLES.COACH:

                return;
            default:
                break;
        }

    }

    const athletePreferredUrlAPI = async () => {
        const payload = {
            profileUrl: `/${roleType}/${userId}-${preferredName?.trim()}`
        }
        try {
            const resultAction = await dispatch(putProfileUrl(payload))
            if (putProfileUrl.fulfilled.match(resultAction)) {
                setOpenModal(false)
                await dispatch(fetchAthleteProfileUrl())
            }
        } catch (error) {
            console.log(error)
        }
    }

    const athleteProfilePasscodeAPI = async (method) => {
        const payload = {
            userId: userId!,
            passcode: method === 'save' ? passcode! : ''
        }
        try {
            const resultAction = await dispatch(postAthleteProfilePasscode({payload, method}))
            if (postAthleteProfilePasscode.fulfilled.match(resultAction)) {
                setOpenPasscodeModal(false)
                await dispatch(fetchAthletePasscode())
            }
        } catch (error) {
            console.log(error)
        }
    }


    const handleSaveProfileUrl = async () => {
        if (profileUrl) {
            switch (roleId) {
                case ROLES.ATHLETE:
                    athletePreferredUrlAPI()
                    return;
                case ROLES.COACH:

                    return;
                default:
                    break;
            }
        }
    };

    const postProfilePasscode = async (method) => {
        switch (roleId) {
            case ROLES.ATHLETE:
                athleteProfilePasscodeAPI(method)
                return;
            case ROLES.COACH:

                return;
            default:
                break;
        }
    }

    return (
        <>
            <div className="flex flex-col gap-2 justify-center">
                <div className="flex items-center justify-center gap-2">
                    <a
                        href={`${process.env.NEXT_PUBLIC_CONNECT_ATHLETE_URL}${profileUrl}`}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="w-full md:w-auto break-words text-center text-wrap font-semibold text-xl hover:text-blue-600 hover:underline"
                    >
                        {`${process.env.NEXT_PUBLIC_CONNECT_ATHLETE_URL}${profileUrl}`}
                    </a>

                    <div className='flex gap-2 items-center'>
                        {coppied ? (
                            <CheckCheck
                                className='cursor-pointer text-gray-800'
                                size={14}
                            />
                        ) : (
                            <Copy
                                className='cursor-pointer text-gray-800'
                                size={14}
                                onClick={handleCopyUrl}
                            />
                        )}
                    </div>

                    <Dialog open={openModal} onOpenChange={() => setOpenModal(!openModal)}>
                        <TooltipProvider>
                            <Tooltip>
                                <DialogTrigger asChild>
                                    <TooltipTrigger asChild>
                                        <Button size="icon" variant="outline"><PencilLine /> </Button>
                                    </TooltipTrigger>
                                </DialogTrigger>
                                <TooltipContent>
                                    <p>You can edit athlete preferred name</p>
                                </TooltipContent>
                            </Tooltip>
                        </TooltipProvider>

                        <DialogContent className="sm:max-w-md" onInteractOutside={(event) => event.preventDefault()}>
                            <DialogHeader>
                                <DialogTitle>Edit Profile Url</DialogTitle>
                            </DialogHeader>

                            <div className="grid gap-4 py-4 w-full">
                                <div className="grid w-full gap-2">
                                    <label htmlFor="profile-url" className="text-sm font-medium">
                                        Your Profile Url Preferred Name
                                    </label>
                                    <Input
                                        className="w-full"
                                        id="profile-url"
                                        placeholder="Enter preferred name"
                                        value={preferredName}
                                        onChange={(e) => {
                                            const sanitizedValue = e.target.value
                                                ?.trimStart()
                                                ?.replace(preventSpaces, '');
                                            setPreferredName(sanitizedValue)
                                        }}
                                    />
                                    {preferredName &&
                                        <span className="text-sm text-wrap break-words break-all w-full text-green-600">
                                            Your URL : {process.env.NEXT_PUBLIC_CONNECT_ATHLETE_URL}/{roleType}/{userId}-{preferredName}
                                        </span>}
                                </div>
                            </div>

                            <DialogFooter className="flex justify-end gap-2">
                                <DialogClose asChild>
                                    <Button variant="outline">Close</Button>
                                </DialogClose>
                                <Button onClick={handleSaveProfileUrl} disabled={!preferredName}>Save</Button>
                            </DialogFooter>
                        </DialogContent>
                    </Dialog>
                </div>

                <div className="flex gap-6 items-center  justify-center">
                    {/* Passcode Profile */}
                    <TooltipProvider>
                        <Tooltip>
                            <Dialog open={openPasscodeModal} onOpenChange={() => setOpenPasscodeModal(!openPasscodeModal)}>
                                <TooltipProvider>
                                    <Tooltip>
                                        <DialogTrigger asChild>
                                            <TooltipTrigger asChild>
                                                <img src={fetchedProfilePasscode ? "/lock-password.svg" : '/unlock-password.svg'} alt={fetchedProfilePasscode ? 'Locked' : 'Unlocked'} className="h-8 fill-current cursor-pointer" />
                                            </TooltipTrigger>
                                        </DialogTrigger>
                                        <TooltipContent>
                                            <p>{fetchedProfilePasscode ? 'Profile Protected' : 'Protect Profile With Passcode'}</p>
                                        </TooltipContent>
                                    </Tooltip>
                                </TooltipProvider>

                                <DialogContent className="sm:max-w-md" onInteractOutside={(event) => event.preventDefault()}>
                                    <DialogHeader>
                                        <DialogTitle>Profile Passcode</DialogTitle>
                                    </DialogHeader>

                                    <div className="grid gap-5 py-4 w-full">
                                        {fetchedProfilePasscode && <div className="flex items-center justify-between gap-3">
                                            <div className="flex flex-col gap-1">
                                                <p>Your Current Password</p>
                                                <div className="flex items-center gap-3">
                                                    <p>{showPasscode ? fetchedProfilePasscode : '******'}</p>
                                                    <button className="border-none bg-transparent outline-none" onClick={() => setShowPasscode(!showPasscode)}>
                                                        {showPasscode ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}</button>
                                                </div>
                                            </div>
                                            <Button variant={'destructive'} size={'icon'} onClick={() => postProfilePasscode('delete')}>
                                                <Trash2 />
                                            </Button>
                                        </div>}
                                        <div className="grid w-full gap-2">
                                            <label htmlFor="profile-url" className="text-sm font-medium">
                                                Profile Passcode
                                            </label>
                                            <Input
                                                className="w-full"
                                                id="profile-passcode"
                                                placeholder="Enter profile passcode"
                                                value={passcode}
                                                onChange={handleChangePasscode}
                                            />
                                        </div>
                                    </div>

                                    <DialogFooter className="flex justify-end gap-2">
                                        <DialogClose asChild>
                                            <Button variant="outline">Close</Button>
                                        </DialogClose>
                                        <Button onClick={() => postProfilePasscode('save')} disabled={passcode?.length <= 4 || loading}>
                                            {loading ? <Loader className='animate-spin h-4 w-4' /> : 'Save'}
                                        </Button>
                                    </DialogFooter>
                                </DialogContent>
                            </Dialog>
                        </Tooltip>
                    </TooltipProvider>

                    <TooltipProvider>
                        <Tooltip>
                            <TooltipTrigger>
                                <Switch checked={toggleProfile} onCheckedChange={handleToggleProfile} />
                            </TooltipTrigger>
                            <TooltipContent>
                                <p>Hide/Publish Athlete Profile Public View</p>
                            </TooltipContent>
                        </Tooltip>
                    </TooltipProvider>
                </div>
            </div>
        </>
    )
}
export default ProfileUrl