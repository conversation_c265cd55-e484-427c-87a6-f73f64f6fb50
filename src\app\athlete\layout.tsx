'use client'
import LeftPanel from "@/components/athleteHome/LeftPanel";
import RightPanel from "@/components/athleteHome/RightPanel";
import AuthorizedFooter from "@/components/AuthorizedFooter";
import AuthorizedNavbar from "@/components/AuthorizedNavbar";
import ClientGuard from "@/components/ClientGuard";
import FloatingMessagesWidget from "@/components/messages/FloatingMessagesWidget";
import { ROLES } from "@/utils/constants";
import { usePathname } from "next/navigation";

const AthleteHomeLayout = ({ children }) => {
    const pathname = usePathname();
    const segments = pathname.split("/").filter(Boolean);
    const validSlugPattern = /^(\d+)-[a-zA-Z-]+$/;
    const isProfilePage = pathname?.startsWith("/athlete/profile");
    const isSlugPage = segments[0] === "athlete" && validSlugPattern.test(segments[1] || "");

    if (isProfilePage || isSlugPage) {
        return <>{children}</>;
    }

    return (
        <div className="font-sans antialiased bg-gray-50 flex flex-col min-h-screen">
            <AuthorizedNavbar />
            <ClientGuard allowedRoles={[ROLES.ATHLETE]}>
                <div className="min-h-screen bg-gray-50 space-y-4 w-full p-5">
                    <div className="max-w-8xl mx-auto flex flex-col lg:flex-row lg:space-x-6">
                        {/* Left Panel */}
                        {/* <aside className="lg:w-1/4 mb-6 lg:mb-0">
                            <LeftPanel />
                        </aside> */}

                        {/* Main Feed */}
                        <main className="lg:w-2/4">
                            {children}
                        </main>

                        {/* Right Panel */}
                        {/* <aside className="lg:w-1/4 mt-6 mb-6 lg:mt-0">
                            <div className="sticky top-20">
                                <RightPanel />
                            </div>
                        </aside> */}
                    </div>
                </div>

                {/* Floating Messages Widget */}
                <FloatingMessagesWidget />
            </ClientGuard>
            <AuthorizedFooter />
        </div>
    )
}
export default AthleteHomeLayout