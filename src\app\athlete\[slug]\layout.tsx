'use client'

import PublicNavBar from "@/components/common/PublicNavBar"
import SpotLights from "@/components/common/spotlight/SpotLights"

const AthletePublicProfileLayout = ({ children }) => {

    return (
        <div className="font-sans antialiased flex flex-col min-h-screen scrollbar-hide">
            {/* Public nav bar */}
            <PublicNavBar />

            {/* Body */}
            <div className="max-w-7xl w-full mx-auto flex flex-col space-y-6 lg:space-y-0 lg:flex-row lg:space-x-6 p-8">
                <main className="flex flex-col gap-8 lg:w-3/4">
                    {children}
                </main>

                <aside className="lg:w-1/4 mb-6 lg:mb-0">
                    <div className="sticky top-20">
                        <SpotLights />
                    </div>
                </aside>
            </div>

            {/* Footer */}
            <div className="pt-4 w-full flex items-center justify-center bg-white p-4 mt-5">
                <p className="text-center max-w-7xl lg:px-14 font-semibold">
                    © 2025 Connect Athlete. All rights reserved.
                    Connect Athlete™, SPARQX™, SAAR™, and all related technologies are the
                    intellectual property of Connect Athlete and protected by copyright
                    and trademark laws.
                </p>
            </div>
        </div>
    )
}
export default AthletePublicProfileLayout