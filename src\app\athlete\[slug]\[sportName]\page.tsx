'use client'
import { AppDispatch, RootState } from "@/store";
import { fetchPublicAthleteSportsInfo } from "@/store/slices/athlete/athleteProfileSlice";
import { fetchPublicAthleteSportExpose } from "@/store/slices/athlete/athleteSportProfileSlice";
import { extractUserIdFromSlug } from "@/utils/commonFunctions";
import { Params } from "next/dist/shared/lib/router/utils/route-matcher";
import { notFound } from "next/navigation";
import { useEffect } from "react";
import { useDispatch, useSelector } from "react-redux";

type Props = {
    params: Params
};

const AthleteSportPublicProfilePage = ({ params }: Props) => {
    const { publicSportsList } = useSelector((state: RootState) => state.athleteProfile)
    const { publicAthleteSportToggles } = useSelector((state: RootState) => state.athleteSportProfile)
    const { sportName, slug } = params;
    const decodedSportName = decodeURIComponent(sportName);
    const dispatch = useDispatch<AppDispatch>()
    const validSlugPattern = /^(\d+)-([a-zA-Z-]+)\/([a-zA-Z-]+)$/;
    if (!validSlugPattern.test(`${slug}/${decodedSportName?.replace(/\s+/g, '-')}`)) {
        return notFound();
    }
    const userId = extractUserIdFromSlug(slug);
    const sportId = publicSportsList?.length > 0 ? publicSportsList?.find(each => each?.sportName?.toLowerCase() === decodedSportName?.toLowerCase())?.sportId : null;

    useEffect(() => {
        (!userId && !decodedSportName) && notFound()
    }, [userId, decodedSportName])

    useEffect(() => {
        userId && dispatch(fetchPublicAthleteSportsInfo(userId))
    }, [dispatch, userId])

    const initialFetches = async () => {
        if (userId && sportId) {
            await dispatch(fetchPublicAthleteSportExpose({ userId, sportId }))
        }
    }

    useEffect(() => {
        initialFetches()
    }, [dispatch, userId, sportId])

    console.log(userId, sportId, publicAthleteSportToggles)

    return (
        <div className="flex flex-col gap-6 justify-center items-center">
            <p className="text-2xl font-bold text-center">
                Athlete {decodedSportName} Sport Public Profile <br />
                Working in progress
            </p>
        </div>
    )
}
export default AthleteSportPublicProfilePage