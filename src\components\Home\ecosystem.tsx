"use client";
import React, { useEffect, useRef, useCallback } from "react";
import useEmblaCarousel from "embla-carousel-react";
import { useSelector } from "react-redux";
import { RootState } from "@/store";
import parse from "html-react-parser";
import {
  titleParseOptions,
  shortDescriptionParseOptions,
  fileTitleParseOptions,
  fileDescriptionParseOptions,
} from "@/utils/parseOptions";
import { ChevronLeft, ChevronRight } from "lucide-react";

const EcosystemSection = () => {
  const [emblaRef, emblaApi] = useEmblaCarousel({ loop: true, align: "start" });
  const autoplayRef = useRef<NodeJS.Timeout | null>(null);

  const { youthEcosystemSection, youthEcosystemImages, loading } = useSelector(
    (state: RootState) => state.homeCMS
  );

  const startAutoplay = useCallback(() => {
    if (autoplayRef.current) clearInterval(autoplayRef.current);
    autoplayRef.current = setInterval(() => {
      if (emblaApi) emblaApi.scrollNext();
    }, 5000);
  }, [emblaApi]);

  const stopAutoplay = useCallback(() => {
    if (autoplayRef.current) clearInterval(autoplayRef.current);
  }, []);

  const scrollPrev = useCallback(() => {
    stopAutoplay();
    emblaApi?.scrollPrev();
  }, [emblaApi, stopAutoplay]);

  const scrollNext = useCallback(() => {
    stopAutoplay();
    emblaApi?.scrollNext();
  }, [emblaApi, stopAutoplay]);

  useEffect(() => {
    if (emblaApi) startAutoplay();
    return () => stopAutoplay();
  }, [emblaApi, startAutoplay, stopAutoplay]);

  if (loading || !youthEcosystemSection) return null;

  return (
    <section
      className="py-12 bg-[#0D1D3A] px-3 sm:px-8 md:px-16 lg:px-36 xl:px-56"
      onKeyDown={(e) => {
        if (e.key === "ArrowLeft") scrollPrev();
        if (e.key === "ArrowRight") scrollNext();
      }}
    >
      <div className="container mx-auto">
        <div className="text-center mb-12">
          <h2 className="text-3xl font-bold bg-gradient-to-r from-white via-blue-300 to-purple-400 bg-clip-text text-transparent leading-tight mb-4">
            {parse(youthEcosystemSection.title || "", titleParseOptions)}
          </h2>
          <p className="cms-text max-w-3xl mx-auto text-gray-300">
            {parse(
              youthEcosystemSection.description || "",
              shortDescriptionParseOptions
            )}
          </p>
        </div>

        <div className="max-w-6xl mx-auto">
          {/* Carousel wrapper with nav arrows */}
          <div className="relative">
            {/* Left Arrow */}
            <button
              type="button"
              aria-label="Previous"
              onClick={scrollPrev}
              onMouseEnter={stopAutoplay}
              onMouseLeave={startAutoplay}
              className="absolute left-2 top-1/2 -translate-y-1/2 z-10 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 p-2 hover:bg-white/20 active:scale-95 transition shadow-sm focus:outline-none focus:ring-2 focus:ring-white/40"
            >
              <ChevronLeft size={20} className="text-white" />
            </button>

            {/* Right Arrow */}
            <button
              type="button"
              aria-label="Next"
              onClick={scrollNext}
              onMouseEnter={stopAutoplay}
              onMouseLeave={startAutoplay}
              className="absolute right-2 top-1/2 -translate-y-1/2 z-10 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 p-2 hover:bg-white/20 active:scale-95 transition shadow-sm focus:outline-none focus:ring-2 focus:ring-white/40"
            >
              <ChevronRight size={20} className="text-white" />
            </button>

            {/* Embla viewport */}
            <div className="overflow-hidden" ref={emblaRef}>
              <div className="flex -ml-2 md:-ml-4">
                {youthEcosystemImages.map((img) => (
                  <div
                    key={img.id}
                    className="flex-[0_0_100%] md:flex-[0_0_50%] lg:flex-[0_0_33.333%] pl-3 md:pl-6"
                    onMouseEnter={stopAutoplay}
                    onMouseLeave={startAutoplay}
                  >
                    <div className="group relative h-[240px] cursor-pointer">
                      <div className="bg-[#13294B] rounded-2xl p-4 shadow-md border border-white/10 transition hover:-translate-y-1 h-full flex flex-col justify-center text-center">
                        <div className="space-y-2 px-2 overflow-hidden">
                          <h3 className="text-lg font-semibold text-white group-hover:text-transparent group-hover:bg-gradient-to-r group-hover:from-blue-400 group-hover:to-purple-400 group-hover:bg-clip-text transition-all">
                            {parse(img.fileTitle || "", fileTitleParseOptions)}
                          </h3>
                          <p className="text-gray-300 text-sm leading-relaxed line-clamp-3">
                            {parse(
                              img.fileDescription || "",
                              fileDescriptionParseOptions
                            )}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
            {/* End viewport */}
          </div>
        </div>
      </div>
    </section>
  );
};

export default EcosystemSection;
