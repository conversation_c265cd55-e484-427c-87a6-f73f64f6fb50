'use client'
import <PERSON><PERSON>oader from "@/components/common/CALoader";
import CardSkeleton from "@/components/common/CardSkeleton";
import CommonToolTip from "@/components/common/CommonToolTip";
import FilePreview from "@/components/common/FilePreview";
import {
    AlertDialog,
    AlertDialogAction,
    AlertDialogContent,
    AlertDialogDescription,
    AlertDialogFooter,
    AlertDialogHeader,
    AlertDialogTitle
} from "@/components/ui/alert-dialog";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Carousel, CarouselContent } from "@/components/ui/carousel";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow
} from "@/components/ui/table";
import { AppDispatch, RootState } from "@/store";
import { fetchPublicAthleteAchievements, fetchPublicAthleteBio, fetchPublicAthleteGallery, fetchPublicAthleteGrowth, fetchPublicAthleteIntro, fetchPublicAthleteLearning, fetchPublicAthleteLocations, fetchPublicAthleteParentDtls, fetchPublicAthletePhysclStats, fetchPublicAthleteProfileUrl, fetchPublicAthleteSocialMedia, fetchPublicAthleteSportsInfo, fetchPublicAthleteToggles, fetchPublicAthleteVideos, fetchPublicAthleteVirtualSession, postVerifyProfilePasscode } from "@/store/slices/athlete/athleteProfileSlice";
import { extractUserIdFromSlug } from "@/utils/commonFunctions";
import { FetchedAchmntItem, FetchedAthleteIntro, FetchedGalleryItem, FetchedGrowthIntrst, FetchedLocationItem, FetchedParentDeatils, FetchedPhysclStatsData, FetchedSportItem, LearningFetchedItem, PublicVideoItem } from "@/utils/interfaces";
import { format } from "date-fns";
import { motion } from "framer-motion";
import { Link as LinkIcon, Loader } from "lucide-react";
import { notFound } from "next/navigation";
import { useEffect, useRef, useState } from "react";
import { useDispatch, useSelector } from "react-redux";

const ProfileCard = ({ intro, learning, loading }: { intro: FetchedAthleteIntro | null, learning: LearningFetchedItem | null, loading: boolean }) => {
    const [showFull, setShowFull] = useState(false);

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="w-full h-full"
        >
            {loading ?
                <div className="flex items-center justify-between gap-5 bg-blue-50 rounded-lg p-5 w-full">
                    <div className="h-32 w-32 rounded-full object-contain animate-pulse bg-slate-300"></div>
                    <div className="flex flex-col gap-2 w-3/4 h-full">
                        {Array.from({ length: 2 }).map((_, index) => (
                            <Skeleton key={`profile-${index}`} className="h-[25px] rounded-lg bg-slate-300 animate-pulse" />
                        ))}
                        {Array.from({ length: 2 }).map((_, index) => (<div key={index + 'sl'} className="flex items-center justify-start gap-5 w-full">
                            <Skeleton className="h-[25px] w-full rounded-lg bg-slate-300 animate-pulse" />
                            <Skeleton className="h-[25px] w-full rounded-lg bg-slate-300 animate-pulse" />
                        </div>))}
                    </div>
                </div> :
                <div className="flex flex-col gap-5 bg-blue-50 rounded-lg p-5">
                    <div className="grid grid-cols-1 md:grid-cols-3 justify-center items-center gap-6">
                        <div className="flex flex-col items-center gap-3">
                            <div className="bg-gradient-to-tr from-blue-200 to-blue-100 p-2 rounded-full">
                                <img
                                    src={intro?.profileImg ? intro?.profileImg : '/user.svg'}
                                    alt="Profile"
                                    className="h-32 w-32 rounded-full object-contain border-2"
                                    onError={(e) => e.currentTarget.src = "/user.svg"}
                                />
                            </div>
                        </div>

                        <div className="md:col-span-2 flex flex-col gap-4">
                            <div className='flex items-center justify-between gap-4'>
                                <div className='flex items-center justify-between text-2xl font-bold gap-4'>
                                    <p className="capitalize">{intro?.preferredAthleteName || ''}</p>
                                    <p className="capitalize">{intro?.lastName || ''}</p>
                                </div>
                            </div>

                            <div className="break-words">
                                {intro?.blurb?.length! > 100 && !showFull
                                    ? `${intro?.blurb?.slice(0, 100)}...`
                                    : intro?.blurb || ''}
                                {intro?.blurb?.length! > 100 && (
                                    <button type="button" onClick={() => setShowFull(!showFull)} className="text-blue-500 ml-2 text-sm">
                                        {showFull ? 'Show less' : 'Read more'}
                                    </button>
                                )}
                            </div>

                            <div className='grid grid-cols-1 md:grid-cols-2 items-center gap-6 w-full'>
                                <div className='flex flex-col gap-1'>
                                    <Label className='font-semibold'>Gender</Label>
                                    <p>{intro?.gender || 'Not Specified'}</p>
                                </div>

                                <div className='flex flex-col gap-1'>
                                    <Label className='font-semibold'>Age</Label>
                                    <p>{intro?.ageGroup || 'Not Specified'}</p>
                                </div>

                                <div className='flex flex-col gap-1'>
                                    <Label className='font-semibold'>School Name</Label>
                                    <p>{learning?.school?.schoolName || learning?.currentSchoolName || 'Not Specified'}</p>
                                </div>

                                <div className='flex flex-col gap-1'>
                                    <Label className='font-semibold'>Class of (Grad Year)</Label>
                                    <p>{learning?.gradYear || 'Not Specified'}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                    <Separator />
                    <div className="flex justify-center">
                        <p className="text-secondary font-semibold">
                            <span className='text-primary'>Primary Sports/Activities: </span>
                            {intro?.topPrimarySports?.join(" | ") || 'Not Specified'}
                        </p>
                    </div>
                </div>}
        </motion.div>
    )
}

const Bio = ({ bio, intro, loading }: { bio: string, intro: FetchedAthleteIntro | null, loading: boolean }) => {
    if (loading) {
        return (
            <div className="flex flex-col gap-2 w-full bg-blue-50 rounded-lg p-5">
                {Array.from({ length: 4 }).map((_, index) => (
                    <Skeleton key={`bio-${index}`} className="h-[20px] rounded-lg bg-slate-300 animate-pulse" />
                ))}
            </div>
        )
    }

    if (!bio) return null

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="w-full"
        >
            <div className="flex flex-col justify-center items-center gap-4 w-full bg-blue-50 p-5 rounded-lg">
                <h2 className="text-xl font-bold   text-primary  text-center">About {(intro?.preferredAthleteName || '') + " " + (intro?.lastName || '')}</h2>
                <p className="">
                    {bio || 'Bio is not added yet!'}
                </p>
            </div>
        </motion.div>
    )
}

const Learning = ({ learning, loading }: { learning: LearningFetchedItem | null, loading: boolean }) => {
    if (loading) {
        return (
            <div className="flex flex-col gap-2">
                {[...Array(5)].map(each =>
                    <Skeleton key={each + 'gallry'} className="from:bg-slate-200 to:bg-slate-100 animate-pulse h-42 w-48" />
                )}
            </div>
        );
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="w-full h-full"
        >
            <div className="flex flex-col gap-4 bg-blue-50 rounded-lg p-6">
                <h3 className="text-xl text-center text-primary font-bold mb-3">Athlete Learning HighLights</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div className="flex flex-col gap-1 col-span-full justify-center items-center">
                        <Label className="font-semibold">Current School Name</Label>
                        <p>{learning?.school?.schoolName || learning?.currentSchoolName || 'Not Specified'}</p>
                    </div>
                    <div className="col-span-full grid grid-cols-3 items-center justify-center">
                        <div className="flex flex-col items-center gap-1">
                            <Label className="font-semibold">Grad Year</Label>
                            <p>{learning?.gradYear || 'Not Specified'}</p>
                        </div>
                        <div className="flex flex-col items-center gap-1">
                            <Label className="font-semibold">Current Year in School</Label>
                            <p>{learning?.yrInSchool || 'Not Specified'}</p>
                        </div>
                        <div className="flex flex-col items-center gap-1">
                            <Label className="font-semibold">Overall Academic Standing</Label>
                            <p>{learning?.overallProgress || 'Not Specified'}</p>
                        </div>
                    </div>

                    <div className="col-span-full grid grid-cols-3 items-center justify-center">
                        <div className="flex flex-col items-center gap-1">
                            <Label className="font-semibold">GPA</Label>
                            <p>{learning?.gpa || 'Not Specified'}</p>
                        </div>
                        <div className="flex flex-col items-center gap-1">
                            <Label className="font-semibold">SAT</Label>
                            <p>{learning?.satScore || 'Not Specified'}</p>
                        </div>
                        <div className="flex flex-col items-center gap-1">
                            <Label className="font-semibold">ACT</Label>
                            <p>{learning?.actScore || 'Not Specified'}</p>
                        </div>
                    </div>

                    <div className="flex flex-col gap-1 col-span-full justify-center items-center">
                        <Label className="font-semibold">Want to share more about your progress?</Label>
                        <p>{learning?.moreAbtOverallProgress || 'Not Specified'}</p>
                    </div>

                    <div className="flex flex-col gap-2 col-span-full justify-center items-center">
                        <Label className="font-semibold text-lg">Learning Strength Area</Label>
                        <div className="flex items-center gap-3">
                            {learning?.topicStrength?.length! > 0 ? learning?.topicStrength?.map(item =>
                                <div className="bg-secondary text-white rounded-3xl flex items-center justify-center font-bold gap-0 p-1 px-2">
                                    <span className="text-sm capitalize">{item?.topicLabel}</span>
                                </div>
                            ) : 'Not Specified'}
                        </div>
                    </div>

                    <div className="flex flex-col gap-2 col-span-full justify-center items-center">
                        <Label className="font-semibold text-lg">Proud Academic Moment</Label>
                        <div className="flex items-center gap-3">
                            {learning?.academicMmt || 'Not Specified'}
                        </div>
                    </div>
                </div>
            </div>
        </motion.div>
    )
}

const GrowthInterests = ({ growth, loading }: { growth: FetchedGrowthIntrst | null, loading: boolean }) => {
    if (loading) {
        return (
            <div className="flex flex-col gap-2">
                {[...Array(4)].map(each =>
                    <Skeleton key={each + 'gallry'} className="bg-slate-300 animate-pulse h-42 w-48" />
                )}
            </div>
        );
    }

    return (
        <>
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, ease: "easeOut" }}
                className="w-full h-full"
            >
                <div className="flex flex-col gap-4 bg-blue-50 rounded-lg p-6">
                    <div className="flex flex-col items-center justify-center gap-6">
                        <h3 className="text-xl font-bold text-center">Growth & Development Interests</h3>

                        <div className="flex items-center justify-center gap-3">
                            {growth?.interests?.length! > 0 ? growth?.interests?.map(item =>
                                <div className="bg-secondary text-white rounded-3xl flex items-center justify-center font-bold gap-0 p-1 px-2">
                                    <span className="text-sm capitalize">{item?.name}</span>
                                </div>
                            ) : 'Not Specified'}
                        </div>

                        <div className="flex flex-col justify-center items-center gap-3">
                            <Label className="font-semibold text-lg">What else would you like to achieve or accomplish?</Label>
                            <p>{growth?.achievements || 'Not Specified'}</p>
                        </div>
                    </div>
                </div>
            </motion.div>
        </>
    )
}

const SocialMedia = ({ list, loading }) => {
    const visibleList = list?.filter(item => !item.isHidden);

    const platformIcons = {
        X: '/X.jpeg',
        Instagram: "/instagram.svg",
        Facebook: "/facebook.svg",
        YouTube: "/youtube.svg",
    };

    const colors = {
        X: 'hover:bg-gray-100',
        Instagram: "hover:bg-pink-200",
        Facebook: "hover:bg-blue-100",
        YouTube: "hover:bg-red-100",
    }

    if (loading) {
        return (
            <div className="flex items-center gap-2 w-full h-full bg-blue-50 rounded-lg p-5">
                {Array.from({ length: 4 }).map((_, index) => (
                    <Skeleton key={`sm-${index}`} className="h-[25px] rounded-lg bg-slate-300 animate-pulse" />
                ))}
            </div>
        )
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="w-full h-full"
        >
            <div className="flex items-center justify-center w-full">
                <div className="flex items-center flex-wrap gap-4 mt-4">
                    {visibleList?.length > 0 && visibleList?.map((item) => {
                        const Icon = platformIcons[item.socialMedia] || LinkIcon;
                        const color = colors[item.socialMedia]
                        return (
                            <CommonToolTip
                                trigger={
                                    <Card key={item.id} className={`hover:shadow-xl transition-shadow cursor-pointer animate-in p-1 ${color}`}>
                                        <CardContent className="flex flex-col items-center gap-3 p-1">
                                            <a
                                                href={item.socialMediaLink}
                                                target="_blank"
                                                rel="noopener noreferrer"
                                                className="truncate text-primay font-bold hover:underline"
                                            >
                                                <img src={Icon} loading="lazy" className="h-6 w-6 rounded-md" />
                                            </a>
                                        </CardContent>
                                    </Card>
                                }
                                toolTipText={item?.socialMedia}
                            />
                        );
                    })}
                </div>
            </div>
        </motion.div>
    );
};

const PhysicalStats = ({ statsData, loading }: { statsData: FetchedPhysclStatsData | null, loading: boolean }) => {
    if (loading) {
        return (
            <div className="flex flex-col gap-2">
                {[...Array(4)].map(each =>
                    <Skeleton key={each + 'pStasts'} className="bg-slate-300 animate-pulse h-42 w-48" />
                )}
            </div>
        );
    }

    const { phyStatsName1, unitsName1, value1, phyStatsName2, unitsName2, value2,
        phyStatsName3, unitsName3, value3, phyStatsName4, unitsName4, value4 } = statsData ?? {};

    const listOfFourPhysStats = statsData
        ? [
            { name: phyStatsName1, unit: unitsName1, value: value1 || "" },
            { name: phyStatsName2, unit: unitsName2, value: value2 || "" },
            { name: phyStatsName3, unit: unitsName3, value: value3 || "" },
            { name: phyStatsName4, unit: unitsName4, value: value4 || "" },
        ].filter(
            (stat) => stat.name?.trim() || stat.value?.toString().trim()
        )
        : [];


    return (
        <>
            <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5, ease: "easeOut" }}
                className="w-full h-full"
            >
                <div className="flex flex-col gap-6 bg-blue-50 rounded-lg p-6">
                    <h3 className="text-xl font-bold text-center">Physical Stats</h3>
                    <div className="gap-10 grid grid-cols-1 md:grid-cols-3 justify-center items-center flex-wrap w-full">
                        <div className="flex items-center justify-center gap-4 w-full">
                            <img src={'/height.webp'} alt='Height' className="w-6" />
                            <div className="flex flex-col items-center gap-1">
                                <Label>Height in Feet</Label>
                                <span>{statsData?.heightInFeet}</span>
                            </div>
                        </div>

                        <div className="flex flex-col gap-1 items-center">
                            <Label>Height in Inches</Label>
                            <span>{statsData?.heightInches}</span>
                        </div>

                        <div className="flex items-center gap-3 w-full">
                            <img src={'/weight.webp'} alt='Weight' className="w-6" />
                            <div className="flex flex-col  gap-1 items-center">
                                <Label>Weight in lbs</Label>
                                <span>{statsData?.weightInLbs}</span>
                            </div>
                        </div>
                    </div>

                    <div className="flex justify-center w-full">
                        <div className="flex flex-col justify-center gap-3 w-3/4">
                            <Label className="font-semibold text-md text-center">Additional physical stats</Label>
                            <Table className="min-w-[400px] bg-blue-50 shadow-lg rounded-lg overflow-hidden border border-slate-300">
                                <TableHeader>
                                    <TableRow className="bg-blue-100 hover:bg-blue-100">
                                        <TableHead className="text-center text-md font-semibold border border-slate-300">
                                            Physical Stats Name
                                        </TableHead>
                                        <TableHead className="text-center text-md font-semibold border border-slate-300">
                                            Stats Unit
                                        </TableHead>
                                        <TableHead className="text-center text-md font-semibold border border-slate-300">
                                            Stats Value
                                        </TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {listOfFourPhysStats?.map((stat, index) => (
                                        <TableRow
                                            key={index + "stats"}
                                            className={`bg-white hover:bg-slate-50 transition-colors`}
                                        >
                                            <TableCell className="text-center capitalize border border-slate-300">
                                                {stat?.name}
                                            </TableCell>
                                            <TableCell className="text-center border border-slate-300">
                                                {stat?.unit}
                                            </TableCell>
                                            <TableCell className="text-center border border-slate-300">
                                                {stat?.value}
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>
                    </div>
                </div>
            </motion.div>
        </>
    )
}

const Videos = ({ list, loading }: { list: PublicVideoItem[], loading: boolean }) => {
    if (loading) {
        return (
            <div className="flex flex-col gap-2">
                {[...Array(2)].map((_, index) => (
                    <Skeleton
                        key={`gallery-skeleton-${index}`}
                        className="bg-slate-300 w-full h-24 rounded-lg animate-pulse"
                    />
                ))}
            </div>
        );
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="w-full h-full"
        >
            <div className="flex flex-col gap-4 bg-blue-50 rounded-lg p-4">
                <h3 className="text-xl text-primary font-bold">Athlete Quick Video Links</h3>
                {!list?.length ? <p className="w-full text-center font-semibold text-gray-600">
                    No Quick Video Links Added Yet
                </p> :
                    <div className="flex flex-col gap-3">
                        {list?.map((each, idx) => (
                            <Card key={`video-card-${each?.id}`} className="p-0">
                                <CardContent className="px-4 py-2">
                                    {/* <p className="font-semibold capitalize">{each?.videoTitle}</p> */}
                                    <a
                                        href={each?.videoLink}
                                        target="_blank"
                                        rel="noopener noreferrer"
                                        className="break-words underline text-sm hover:text-blue-600 line-clamp-2"
                                    >
                                        {each?.videoTitle}
                                    </a>
                                </CardContent>
                            </Card>
                        ))}
                    </div>}
            </div>
        </motion.div>
    );
};

const Gallery = ({ list, loading }: { list: FetchedGalleryItem[], loading: boolean }) => {
    if (loading) {
        return (
            <div className="flex flex-col gap-2">
                {[...Array(2)].map(each =>
                    <Skeleton key={each + 'gallry'} className="bg-slate-300 animate-pulse h-42 w-48" />
                )}
            </div>
        );
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="w-full h-full"
        >
            <div className="flex flex-col gap-4 bg-blue-50 rounded-lg p-4">
                <h3 className="text-xl text-primary font-bold">Athlete Gallery</h3>
                {!list?.length ?
                    <p className="w-full text-center font-semibold text-gray-600">
                        No Gallery Items Added Yet!
                    </p> :
                    <div className="grid grid-cols-1 gap-3 overflow-y-auto h-96 max-h-[450px] scrollbar-hide">
                        {list?.length > 0 ? list.map(each =>
                            <Card className="p-0">
                                <CardContent className="px-0">
                                    <img src={each?.fileLocation} loading="lazy" className="object-fill h-48 w-full" />
                                    <p className="p-3 line-clamp-2 text-center capitalize">{each?.description}</p>
                                </CardContent>
                            </Card>
                        ) : null}
                    </div>}
            </div>
        </motion.div>
    )
}

const Achievements = ({ list, loading }: { list: FetchedAchmntItem[], loading: boolean }) => {
    const scrollRef = useRef<any>(null);

    useEffect(() => {
        const interval = setInterval(() => {
            if (scrollRef.current) {
                scrollRef.current.scrollTo(
                    (scrollRef.current.selectedScrollSnap() + 1) %
                    list?.length
                )
            }
        }, 2000)

        return () => clearInterval(interval)
    }, [list, scrollRef])


    if (loading) {
        return (
            <div className="flex flex-col gap-2">
                {[...Array(2)].map(each =>
                    <CardSkeleton />
                )}
            </div>
        );
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="w-full h-full"
        >
            <div className="flex flex-col gap-4 bg-blue-50 rounded-lg p-4">
                <h3 className="text-xl text-primary font-bold">Athlete Achievements</h3>
                {!list?.length ?
                    <p className="w-full text-center font-semibold text-gray-600">
                        Achievements are not added yet!
                    </p> :
                    <Carousel
                        opts={{
                            align: "start",
                            loop: true,
                        }}
                        setApi={(api) => (scrollRef.current = api)}
                        className="w-full h-full"
                    >
                        <CarouselContent className="max-h-fit w-full space-x-4 ml-4">
                            {list?.length > 0 &&
                                list.map((item) => (
                                    <Card
                                        className="flex flex-col gap-3 min-w-[300px] max-w-[400px] flex-shrink-0 snap-center"
                                        key={item?.id}
                                    >
                                        <CardHeader>
                                            <CardTitle className="text-lg">{item?.achievementTitle}</CardTitle>
                                        </CardHeader>

                                        <CardContent
                                            className={`grid grid-cols-1 ${item?.s3FileLink ? "md:grid-cols-3" : "md:grid-cols-2"
                                                } gap-3 items-center`}
                                        >
                                            <div className="md:col-span-2">
                                                <span className="font-semibold">
                                                    {item?.achievementDate &&
                                                        format(new Date(item?.achievementDate), "MM-dd-yyyy")}
                                                </span>
                                                <p>{item?.achievementDesc}</p>
                                                <p className="text-secondary font-semibold mt-1 capitalize flex-wrap">
                                                    {item?.tags?.map((each) => each.value)?.join(" , ")}
                                                </p>
                                                <a
                                                    href={item?.achievementLink}
                                                    target="_blank"
                                                    rel="noopener noreferrer"
                                                    className="w-full md:w-auto break-words underline text-sm font-semibold hover:text-blue-600"
                                                >
                                                    {item?.achievementLink}
                                                </a>
                                            </div>

                                            <div className="flex items-center justify-center md:justify-end">
                                                <FilePreview s3FileLink={item?.s3FileLink} />
                                            </div>
                                        </CardContent>
                                    </Card>
                                ))}
                        </CarouselContent>
                    </Carousel>}
            </div>
        </motion.div>
    )
}

const Sports = ({ list, profileUrl, loading }: { list: FetchedSportItem[], profileUrl: string, loading: boolean }) => {
    if (loading) {
        return (
            <div className="flex flex-col gap-2">
                {[...Array(3)].map((_, index) => (
                    <Skeleton
                        key={`gallery-skeleton-${index}`}
                        className="from:bg-slate-200 to:bg-slate-100  w-full h-20 rounded-lg animate-pulse"
                    />
                ))}
            </div>
        );
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="w-full h-full"
        >
            <div className="flex flex-col gap-4 bg-blue-50 rounded-lg p-4">
                <h3 className="text-xl text-primary text-center font-bold">Athlete Sports</h3>
                {!list?.length ? <p className="w-full text-center font-semibold text-gray-600">No Sports added yet!</p> :
                    <div className="flex flex-col gap-3 max-h-[800px] overflow-y-auto scrollbar-hide">
                        {list?.map((item, idx) => (
                            <motion.div
                                initial={{ opacity: 0, y: 20 }}
                                animate={{ opacity: 1, y: 0 }}
                                transition={{ duration: 0.5, ease: "easeOut" }}
                                className="w-full h-full"
                            >
                                <a href={`${process.env.NEXT_PUBLIC_CONNECT_ATHLETE_URL}${profileUrl}/${item?.sportName}`}
                                    target="_blank"
                                    rel="noopener noreferrer"
                                >
                                    <div key={item?.id} className="grid grid-cols-1 sm:grid-cols-5 justify-center items-start flex-wrap gap-1 bg-white rounded-lg shadow-md border border-slate-100 hover:shadow-xl hover:border-slate-300 p-4">
                                        <span className={`text-secondary font-semibold text-center`}>{item?.primarySportFlag === 'Y' ? 'Primary' : ''}</span>
                                        <div className="flex flex-col items-center gap-3 sm:col-span-3 cursor-pointer">
                                            <p className="font-semibold text-center">{item?.levels?.length > 0 && item?.levels?.[0].levelName ? `${item?.sportName} - ${item?.levels?.[0]?.levelName}` : item?.sportName}</p>
                                            <p className="text-center">{item?.specialities?.map(each => each?.specialityName)?.join(" | ")}</p>
                                        </div>
                                    </div>
                                </a>
                            </motion.div>
                        ))}
                    </div>}
            </div>
        </motion.div>
    );
}

const Locations = ({ list, openSession, loading }: { list: FetchedLocationItem[], openSession: string, loading: boolean }) => {

    const CountyWithCities = ({ county }) => {
        const [showAll, setShowAll] = useState(false);
        const filteredCities = county?.cities?.filter(city => city?.cityName !== "All") || [];
        const displayCities = showAll ? filteredCities : filteredCities?.slice(0, 10);

        return (
            <div className="flex items-center px-4">
                <p className="text-wrap font-semibold">
                    {(county?.countyName && county?.countyName !== 'All') ? `${county?.countyName} - ` : ''}

                    <span className="font-light">
                        {displayCities.map(city => city.cityName).join(" | ")}
                        {filteredCities.length > 10 && (
                            <button
                                onClick={() => setShowAll(prev => !prev)}
                                className="text-blue-500 hover:underline ml-2"
                            >
                                {showAll ? "Show Less..." : "Show More..."}
                            </button>
                        )}
                    </span>
                </p>
            </div>
        );
    }

    if (loading) {
        return (
            <div className="flex flex-col gap-2">
                {[...Array(3)].map((_, index) => (
                    <Skeleton
                        key={`locations-skeleton-${index}`}
                        className="bg-slate-300 w-full h-24 rounded-lg animate-pulse"
                    />
                ))}
            </div>
        );
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="w-full h-full"
        >
            <div className="flex flex-col gap-4 bg-blue-50 rounded-lg p-4">
                <h3 className="text-xl text-primary text-center font-bold">Athlete Locations</h3>
                {!list?.length ?
                    <p className="w-full text-center font-semibold text-gray-600">
                        Locations are not added yet!
                    </p>
                    : <>
                        {openSession === 'Y' ? <span>Open to Virtual Sessions</span> : ''}
                        <div className="flex flex-col gap-3 max-h-[800px] overflow-y-auto scrollbar-hide">
                            {list?.map((item, idx) => (
                                <motion.div
                                    initial={{ opacity: 0, y: 20 }}
                                    animate={{ opacity: 1, y: 0 }}
                                    transition={{ duration: 0.5, ease: "easeOut" }}
                                    className="w-full h-full"
                                    key={item?.id}
                                >
                                    <div className="grid grid-cols-1 justify-center items-start flex-wrap gap-1 bg-white rounded-lg shadow-md border border-slate-100 hover:shadow-xl hover:border-slate-300 p-4">
                                        <div className="col-span-3 flex flex-col gap-1">
                                            <h6 className="text-wrap font-semibold text-secondary px-4">
                                                <span className="font-semibold ">{item?.stateName}</span>
                                            </h6>
                                            {item?.counties?.map((county) => (
                                                <CountyWithCities key={county.countyId} county={county} />
                                            ))}
                                        </div>
                                    </div>
                                </motion.div>
                            ))}
                        </div>
                    </>}
            </div>
        </motion.div>
    );
}

const ParentGuardian = ({ parentData, loading }: { parentData: FetchedParentDeatils | null, loading: boolean }) => {
    if (loading) {
        return (
            <div className="flex flex-col gap-2">
                {[...Array(5)].map(each =>
                    <Skeleton key={each + 'gallry'} className="bg-slate-300 animate-pulse h-42 w-48" />
                )}
            </div>
        );
    }

    return (
        <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, ease: "easeOut" }}
            className="w-full h-full"
        >
            <div className="flex flex-col gap-4 bg-blue-50 rounded-lg p-6">
                <h3 className="text-xl text-center text-primary font-bold mb-3">Primary Parent/ Legal Guardian Contact Details</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    <div className="flex flex-col gap-1 justify-center items-center">
                        <Label className="font-semibold">First Name</Label>
                        <p>{parentData?.parentFirstName || 'Not Specified'}</p>
                    </div>
                    <div className="flex flex-col gap-1 justify-center items-center">
                        <Label className="font-semibold">Last Name</Label>
                        <p>{parentData?.parentLastName || 'Not Specified'}</p>
                    </div>
                    {!parentData?.isHiddenMobile && <div className="flex flex-col gap-1 justify-center items-center">
                        <Label className="font-semibold">Contact</Label>
                        <p>{parentData?.parentPhone || 'Not Specified'}</p>
                    </div>}
                    {!parentData?.isHiddenEmail && <div className="flex flex-col gap-1 justify-center items-center">
                        <Label className="font-semibold">Email</Label>
                        <p>{parentData?.parentEmail || 'Not Specified'}</p>
                    </div>}
                    {!parentData?.isHiddenPrimaryParentReln && <div className="flex flex-col gap-1 justify-center items-center">
                        <Label className="font-semibold">Relation</Label>
                        <p>{parentData?.primaryParentReln || 'Not Specified'}</p>
                    </div>}
                </div>
            </div>
        </motion.div>
    )
}

const AthleteProfilePublicPage = ({ params }: { params: { slug: string } }) => {
    const { apiStatus, publicAthleteIntro, publicAthelteBio, publicSocialMediaList,
        publicAthleteToggles, publicLearning, publicVideos, publicGallery,
        publicParentDetails, publicSportsList, publicAchmentsList, publicGrowthIntrsData,
        publicPhysicalStats, publicVertualSession, publicLocations, profileUrl
    } = useSelector((state: RootState) => state.athleteProfile)
    const isPasswordVerified = JSON.parse(localStorage.getItem('isPasscodeVerified') || 'false');
    const [passcode, setPasscode] = useState('')
    const [openPasscodeModal, setOpenPasscodeModal] = useState(publicAthleteToggles?.passcodePrivacy && isPasswordVerified)
    const dispatch = useDispatch<AppDispatch>()
    const { slug } = params;
    const validSlugPattern = /^(\d+)-[a-zA-Z-]+$/;
    if (!validSlugPattern.test(slug)) return notFound();
    const userId = extractUserIdFromSlug(slug);

    const verifyProfilePasscode = async () => {
        try {
            const resultAction = await dispatch(postVerifyProfilePasscode({ userId: userId!, passcode }))
            if (postVerifyProfilePasscode.fulfilled.match(resultAction)) {
                setOpenPasscodeModal(false)
                localStorage.setItem('isPasscodeVerified', 'true')
                await dispatch(fetchPublicAthleteToggles(userId!))
            }
        } catch (error) {
            console.log(error)
        }
    }

    useEffect(() => {
        !userId && notFound()
    }, [userId])

    useEffect(() => {
        const get = async () => {
            userId && await dispatch(fetchPublicAthleteToggles(userId))
        }
        get()
    }, [userId, dispatch])

    const initialFetches = async () => {
        if (userId && publicAthleteToggles?.profile) {
            await dispatch(fetchPublicAthleteIntro(userId))
            await dispatch(fetchPublicAthleteBio(userId))
            await dispatch(fetchPublicAthleteLearning(userId))
            publicAthleteToggles?.physicalStats && await dispatch(fetchPublicAthletePhysclStats(userId!))
            await dispatch(fetchPublicAthleteProfileUrl(userId))
            await dispatch(fetchPublicAthleteSportsInfo(userId))
            await dispatch(fetchPublicAthleteLocations(userId))
            await dispatch(fetchPublicAthleteVirtualSession(userId))
            publicAthleteToggles?.quickLinks && await dispatch(fetchPublicAthleteVideos(userId!))
            publicAthleteToggles?.gallery && await dispatch(fetchPublicAthleteGallery(userId!))
            publicAthleteToggles?.achievements && await dispatch(fetchPublicAthleteAchievements(userId!))
            await dispatch(fetchPublicAthleteGrowth(userId))
            publicAthleteToggles?.parentGuardian && await dispatch(fetchPublicAthleteParentDtls(userId!))
            publicAthleteToggles?.socialMedia && await dispatch(fetchPublicAthleteSocialMedia(userId!))
        }
    }

    useEffect(() => {
        if (userId && publicAthleteToggles?.profile) {
            initialFetches()
            setOpenPasscodeModal(publicAthleteToggles?.passcodePrivacy)
        }
    }, [publicAthleteToggles, userId, dispatch])

    if (apiStatus === 'pblcTogglePending') {
        return <CALoader />
    }

    if (!publicAthleteToggles?.profile) {
        return (
            <div className="flex items-center justify-center h-[80%]">
                <div className="flex flex-col items-center justify-center text-center p-6">
                    <img src='/denied.png' className="h-16 w-16" />
                    <h2 className="text-2xl font-semibold text-orange-600">No Access</h2>
                    <p className="text-lg text-orange-600 mt-1 max-w-sm">
                        This profile is not accessable at this moment. please check later
                    </p>
                </div>
            </div>
        )
    }

    return (
        <>
            <AlertDialog
                open={openPasscodeModal && !isPasswordVerified}
                onOpenChange={setOpenPasscodeModal}
            >
                <AlertDialogContent className="sm:max-w-md">
                    <AlertDialogHeader>
                        <AlertDialogTitle>Verify Profile Passcode</AlertDialogTitle>
                        <AlertDialogDescription asChild>
                            <div className="grid gap-4 py-4 w-full">
                                <div className="grid w-full gap-2">
                                    <label
                                        htmlFor="profile-passcode"
                                        className="text-sm font-medium"
                                    >
                                        Profile Passcode
                                    </label>
                                    <Input
                                        id="profile-passcode"
                                        placeholder="Enter profile passcode"
                                        value={passcode}
                                        onChange={(e) => setPasscode(e.target.value)}
                                    />
                                </div>
                            </div>
                        </AlertDialogDescription>
                    </AlertDialogHeader>

                    <AlertDialogFooter>
                        <AlertDialogAction
                            onClick={verifyProfilePasscode}
                            disabled={
                                passcode?.length < 4 ||
                                apiStatus === "verifyPasscodePending"
                            }
                        >
                            {apiStatus === "verifyPasscodePending" ? (
                                <Loader className="animate-spin h-4 w-4" />
                            ) : (
                                "Save"
                            )}
                        </AlertDialogAction>
                    </AlertDialogFooter>
                </AlertDialogContent>
            </AlertDialog>

            <div className="flex flex-col gap-6">
                <ProfileCard intro={publicAthleteIntro} learning={publicLearning} loading={apiStatus === 'pblcIntroPending'} />
                <Learning learning={publicLearning} loading={apiStatus === 'pblcLearningPending'} />
                {publicAthleteToggles?.physicalStats && <PhysicalStats statsData={publicPhysicalStats} loading={apiStatus === 'pblcPhclStatsPending'} />}
                <div className="grid grid-cols-1 lg:grid-cols-6 gap-6 items-stretch w-full h-full">
                    <div className="flex flex-col gap-6 lg:col-span-3 items-stretch h-full">
                        <Sports list={publicSportsList} profileUrl={profileUrl} loading={apiStatus === 'pblcSportsPending'} />
                    </div>
                    <div className="lg:col-span-3 h-full max-h-full items-stretch">
                        <Locations list={publicLocations} openSession={publicVertualSession} loading={apiStatus === 'pblcLocationsPending'} />
                    </div>
                </div>
                <div className="grid grid-cols-1 lg:grid-cols-6 gap-6 items-stretch h-full">
                    <div className="flex flex-col gap-6 lg:col-span-3 items-stretch h-full">
                        <Bio bio={publicAthelteBio || ''} intro={publicAthleteIntro} loading={apiStatus === 'pblcBioPending'} />
                        {publicAthleteToggles?.quickLinks && <Videos list={publicVideos} loading={apiStatus === 'pblcVideosPending'} />}
                    </div>
                    <div className="lg:col-span-3 h-full max-h-full items-stretch">
                        {publicAthleteToggles?.gallery && <Gallery list={publicGallery} loading={apiStatus === 'pblcGalleryPending'} />}
                    </div>
                </div>
                {publicAthleteToggles?.achievements && <Achievements list={publicAchmentsList} loading={apiStatus === 'pblcAchmntsPending'} />}
                <GrowthInterests growth={publicGrowthIntrsData} loading={apiStatus === 'pblcGrowthPending'} />
                {publicAthleteToggles?.parentGuardian && <ParentGuardian parentData={publicParentDetails} loading={apiStatus === 'pblcParentDtlsPending'} />}
                {publicAthleteToggles?.socialMedia && <SocialMedia list={publicSocialMediaList} loading={apiStatus === 'pblcSMPending'} />}
            </div>
        </>
    )
}
export default AthleteProfilePublicPage