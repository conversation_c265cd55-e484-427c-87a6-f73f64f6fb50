"use client";

import { MessageCircle, Minus, X, ExternalLink } from "lucide-react";
import { useState, useEffect } from "react";
import { AnimatePresence, motion } from "framer-motion";
import Link from "next/link";
import Avatar from "../common/Avatar";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { useDispatch, useSelector } from "react-redux";
import { AppDispatch, RootState } from "@/store";
import { fetchMessagesData } from "@/store/slices/commonSlice";
import { ConnectedUser } from "@/types/messages";
import { usePathname } from "next/navigation";

const FloatingMessagesWidget = () => {
    const [isExpanded, setIsExpanded] = useState(false);
    const [selectedConversation, setSelectedConversation] = useState<ConnectedUser | null>(null);
    const [newMessage, setNewMessage] = useState("");

    const pathname = usePathname();
    const dispatch = useDispatch<AppDispatch>();
    const { messagesConnectedUsers } = useSelector((state: RootState) => state.commonSlice);

    useEffect(() => {
        dispatch(fetchMessagesData());
    }, [dispatch]);

    const shouldHide =
        pathname === "/athlete/messages" || pathname === "/coach/messages";

    if (shouldHide) {
        return null;
    }

    const handleToggleExpanded = () => {
        setIsExpanded(!isExpanded);
        if (!isExpanded) {
            setSelectedConversation(null);
        }
    };

    // Handle keyboard shortcuts
    //   useEffect(() => {
    //     const handleKeyDown = (event: KeyboardEvent) => {
    //       // ESC to close widget
    //       if (event.key === 'Escape' && isExpanded) {
    //         if (selectedConversation) {
    //           setSelectedConversation(null);
    //         } else {
    //           setIsExpanded(false);
    //         }
    //       }
    //     };

    //     document.addEventListener('keydown', handleKeyDown);
    //     return () => document.removeEventListener('keydown', handleKeyDown);
    //   }, [isExpanded, selectedConversation]);

    const handleSelectConversation = (conversation: ConnectedUser) => {
        setSelectedConversation(conversation);
    };

    const handleSendMessage = (e: React.FormEvent) => {
        e.preventDefault();
        if (newMessage.trim() && selectedConversation) {
            // TODO: Implement socket message sending
            console.log("Sending message:", newMessage, "to:", selectedConversation.id);
            setNewMessage("");
        }
    };

    const formatTime = (dateString: string) => {
        if (!dateString) return "";

        const date = new Date(dateString);
        if (isNaN(date.getTime())) return "";

        const now = new Date();
        const diff = now.getTime() - date.getTime();
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));

        if (minutes < 1) return "now";
        if (minutes < 60) return `${minutes}m`;
        if (hours < 24) return `${hours}h`;
        return date.toLocaleDateString();
    };

    const formatMessageTime = (dateString: string) => {
        if (!dateString) return "";
        const date = new Date(dateString);
        if (isNaN(date.getTime())) return "";
        return date.toLocaleTimeString([], { hour: "2-digit", minute: "2-digit" });
    };

    // Function to detect and render links in text
    const renderMessageWithLinks = (text: string) => {
        const urlRegex = /(https?:\/\/[^\s]+|www\.[^\s]+|[^\s]+\.[a-z]{2,}(?:\/[^\s]*)?)/gi;
        const parts = text.split(urlRegex);

        return parts.map((part, index) => {
            if (urlRegex.test(part)) {
                let url = part;
                if (!part.startsWith('http://') && !part.startsWith('https://')) {
                    url = 'https://' + part;
                }

                return (
                    <a
                        key={index}
                        href={url}
                        target="_blank"
                        rel="noopener noreferrer"
                        className="underline hover:no-underline font-medium text-blue-300"
                        onClick={(e) => e.stopPropagation()}
                    >
                        {part}
                    </a>
                );
            }
            return part;
        });
    };

    return (
        <div className="fixed bottom-6 right-6 z-50">
            <AnimatePresence>
                {isExpanded && (
                    <motion.div
                        initial={{ opacity: 0, scale: 0.8, y: 20 }}
                        animate={{ opacity: 1, scale: 1, y: 0 }}
                        exit={{ opacity: 0, scale: 0.8, y: 20 }}
                        transition={{ duration: 0.2 }}
                        className="mb-4 bg-white rounded-lg shadow-xl border border-gray-200 w-80 h-[500px] flex flex-col overflow-hidden"
                    >
                        {/* Header */}
                        <div className="p-4 border-b border-gray-200 bg-gray-50">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h3 className="text-lg font-semibold text-gray-900">
                                        {selectedConversation ? `${selectedConversation.firstName} ${selectedConversation.lastName}` : "Messages"}
                                    </h3>
                                    {!selectedConversation && messagesConnectedUsers.length > 0 && (
                                        <p className="text-xs text-gray-500">
                                            {messagesConnectedUsers.length} conversation{messagesConnectedUsers.length !== 1 ? 's' : ''}
                                        </p>
                                    )}
                                </div>
                                <div className="flex items-center space-x-1">
                                    {selectedConversation && (
                                        <Button
                                            variant="ghost"
                                            size="icon"
                                            onClick={() => setSelectedConversation(null)}
                                            className="h-8 w-8 text-gray-500 hover:text-gray-700"
                                            title="Back to conversations"
                                        >
                                            <Minus className="w-4 h-4" />
                                        </Button>
                                    )}
                                    <Button
                                        variant="ghost"
                                        size="icon"
                                        onClick={handleToggleExpanded}
                                        className="h-8 w-8 text-gray-500 hover:text-gray-700"
                                        title="Close messages"
                                    >
                                        <X className="w-4 h-4" />
                                    </Button>
                                </div>
                            </div>
                        </div>

                        {/* Content */}
                        <div className="flex-1 flex flex-col overflow-hidden">
                            {selectedConversation ? (
                                /* Chat View - Simplified for now */
                                <div className="flex flex-col h-full">
                                    {/* Messages */}
                                    <div className="flex-1 overflow-y-auto p-3 space-y-3">
                                        <div className="text-center text-gray-500 text-sm">
                                            Chat with {selectedConversation.firstName} {selectedConversation.lastName}
                                        </div>
                                        {selectedConversation.lastMessage && (
                                            <div className="flex justify-start">
                                                <div className="max-w-[70%] px-3 py-2 rounded-lg text-sm bg-gray-100 text-gray-900 rounded-bl-sm">
                                                    <div>{renderMessageWithLinks(selectedConversation.lastMessage.content)}</div>
                                                    <p className="text-xs mt-1 text-gray-500">
                                                        {formatMessageTime(selectedConversation.lastMessage.createdAt)}
                                                    </p>
                                                </div>
                                            </div>
                                        )}
                                    </div>

                                    {/* Message Input */}
                                    <div className="p-3 border-t border-gray-200">
                                        <form onSubmit={handleSendMessage} className="flex space-x-2">
                                            <Input
                                                type="text"
                                                placeholder="Type a message..."
                                                value={newMessage}
                                                onChange={(e) => setNewMessage(e.target.value)}
                                                className="flex-1 h-8 text-sm"
                                            />
                                            <Button
                                                type="submit"
                                                size="sm"
                                                disabled={!newMessage.trim()}
                                                className="h-8 px-3 text-xs"
                                            >
                                                Send
                                            </Button>
                                        </form>
                                    </div>
                                </div>
                            ) : (
                                /* Conversations List */
                                <div className="flex flex-col h-full">
                                    <div className="flex-1 overflow-y-auto floating-messages-scroll relative">
                                        {messagesConnectedUsers.length > 0 ? messagesConnectedUsers.map((conversation) => (
                                            <div
                                                key={conversation.id}
                                                onClick={() => handleSelectConversation(conversation)}
                                                className="p-3 border-b border-gray-100 cursor-pointer hover:bg-gray-50 transition-colors"
                                            >
                                                <div className="flex items-center space-x-3">
                                                    <div className="relative">
                                                        <Avatar
                                                            profileImg={conversation.galleries[0]?.fileLocation || ""}
                                                            name={conversation.firstName.charAt(0)}
                                                            styles="h-10 w-10 bg-slate-700 text-white"
                                                        />
                                                    </div>

                                                    <div className="flex-1 min-w-0">
                                                        <div className="flex items-center justify-between mb-1">
                                                            <h4 className="text-sm font-medium text-gray-900 truncate">
                                                                {conversation.firstName} {conversation.lastName}
                                                            </h4>
                                                            {conversation.lastMessage && (
                                                                <span className="text-xs text-gray-500">
                                                                    {formatTime(conversation.lastMessage.createdAt)}
                                                                </span>
                                                            )}
                                                        </div>

                                                        <div className="flex items-center justify-between">
                                                            <p className="text-xs text-gray-600 truncate">
                                                                {conversation.lastMessage?.content
                                                                    ? conversation.lastMessage.content.replace(/(https?:\/\/[^\s]+|www\.[^\s]+|[^\s]+\.[a-z]{2,}(?:\/[^\s]*)?)/gi, '[Link]')
                                                                    : "No messages yet"}
                                                            </p>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        )) : (
                                            <div className="p-6 text-center text-gray-500">
                                                <MessageCircle className="w-8 h-8 mx-auto mb-2 text-gray-300" />
                                                <p className="text-sm">No conversations yet</p>
                                            </div>
                                        )}
                                    </div>

                                    {/* View All Messages Link - Fixed at bottom */}
                                    {messagesConnectedUsers.length > 0 && (
                                        <div className="p-3 border-t border-gray-200 bg-gray-50 flex-shrink-0">
                                            <Link
                                                href="/athlete/messages"
                                                className="flex items-center justify-center space-x-2 text-sm text-blue-600 hover:text-blue-700 font-medium"
                                                onClick={() => setIsExpanded(false)}
                                            >
                                                <span>View all messages</span>
                                                <ExternalLink className="w-3 h-3" />
                                            </Link>
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                    </motion.div>
                )}
            </AnimatePresence>

            {/* Floating Button */}
            <Button
                onClick={handleToggleExpanded}
                className="w-14 h-14 rounded-full bg-blue-600 hover:bg-blue-700 text-white shadow-lg hover:shadow-xl transition-all duration-200"
                title="Open messages"
            >
                <MessageCircle className="w-6 h-6" />
                {messagesConnectedUsers.length > 0 && (
                    <span className="absolute -top-1 -right-1 w-5 h-5 bg-red-500 text-white text-xs rounded-full flex items-center justify-center">
                        {messagesConnectedUsers.length > 9 ? "9+" : messagesConnectedUsers.length}
                    </span>
                )}
            </Button>
        </div>
    );
};

export default FloatingMessagesWidget;
