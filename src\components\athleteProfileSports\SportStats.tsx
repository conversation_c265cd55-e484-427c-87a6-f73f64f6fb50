'use client'
import {
    <PERSON><PERSON>,
    DialogContent,
    Di<PERSON>Title,
    DialogTrigger
} from "@/components/ui/dialog"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"
import { useLocalStoredInfo } from "@/hooks/useLocalStoredInfo"
import { useTokenValues } from "@/hooks/useTokenValues"
import { AppDispatch, RootState } from "@/store"
import { deleteAthleteSportStats, fetchAthleteSportsStats, postAthleteSportStats, putAthleteSportStats } from "@/store/slices/athlete/athleteSportProfileSlice"
import { fetchSportStatNames } from "@/store/slices/commonSlice"
import { preventSpaces } from "@/utils/validations"
import { zodResolver } from '@hookform/resolvers/zod'
import { ColumnDef } from "@tanstack/react-table"
import { format } from "date-fns"
import { Loader, Pencil, Plus, Trash2 } from "lucide-react"
import React, { useEffect, useMemo, useState } from "react"
import { Controller, useForm } from "react-hook-form"
import { RiResetLeftLine } from "react-icons/ri"
import { useDispatch, useSelector } from "react-redux"
import { z } from 'zod'
import AlertPopup from "../common/AlertPopup"
import CommonCalender from "../common/CommonCalender"
import { GlobalSortPaginationTable } from "../common/GlobalSortPaginationTable"
import NumericInput from "../common/NumericInput"
import SearchInput from "../common/SearchInput"
import UpgradePremiumSection from "../common/UpgradePremiumSection"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import { Label } from "../ui/label"
import { Switch } from "../ui/switch"

interface IProps {
    sportName: string
    sportId: number;
}

const schema = z
    .object({
        seasonName: z
            .string()
            .min(1, 'Season name is required'),
        date: z.date({
            required_error: "Date is required.",
        }),
        statsName: z.any().optional(),
        statsValueNumeric: z.any().optional(),
        statsValueText: z.any().optional(),
        isPublished: z.any().optional(),
    })

const SportStats = ({ sportName, sportId }: IProps) => {
    const [date, setDate] = useState<Date | undefined>(undefined);
    const [publishFilter, setPublishFilter] = useState<string>('');
    const [openModal, setOpenModal] = useState(false);
    const [editingRowId, setEditingRowId] = useState<number | null>(null);
    const [editedData, setEditedData] = useState<Record<number, any>>({});
    const { statsFormData, statsList, apiStatus } = useSelector((state: RootState) => state.athleteSportProfile);
    const { sportStatNamesList } = useSelector((state: RootState) => state.commonSlice);
    const dispatch = useDispatch<AppDispatch>();
    const { roleId, userId, isPremiumUser } = useTokenValues();
    const { profileId } = useLocalStoredInfo()

    const initialFetches = async () => {
        await dispatch(fetchSportStatNames());
        await dispatch(fetchAthleteSportsStats({ sportId }));
    };

    useEffect(() => {
        initialFetches();
    }, [dispatch]);

    type FormData = z.infer<typeof schema>;

    const {
        control,
        handleSubmit,
        reset,
        formState: { errors },
    } = useForm<FormData>({
        resolver: zodResolver(schema),
        defaultValues: {
            ...statsFormData,
            seasonName: statsFormData?.seasonName ?? '',
            date: statsFormData?.date ? new Date(statsFormData.date) : undefined,
            statsName: statsFormData?.statsName! ?? null,
            statsValueNumeric: statsFormData?.statsValueNumeric! ?? '',
            statsValueText: statsFormData?.statsValueText! ?? '',
            isPublished: statsFormData?.isPublished ?? undefined,
        }
    });

    useEffect(() => {
        if (openModal) {
            reset();
        }
    }, [openModal, reset]);

    const onSubmit = async (data: FormData) => {
        const payload = {
            roleId,
            athleteId: profileId,
            userId,
            sportId,
            statDate: format(new Date(data?.date), 'yyyy-MM-dd') || null,
            statNameUnitId: data?.statsName?.value || '',
            statValueNumeric: Number(data?.statsValueNumeric) || '',
            statValueText: data?.statsValueText || '',
            seasonName: data?.seasonName || '',
            isHidden: data?.isPublished ? "Unhide" : "Hide",
            isDeleted: false
        };

        try {
            const resultAction = await dispatch(postAthleteSportStats(payload));
            if (postAthleteSportStats.fulfilled.match(resultAction)) {
                reset();
                setOpenModal(false);
                await dispatch(fetchAthleteSportsStats({ sportId }));
            }
        } catch (error) {
            console.log(error);
        }
    };

    const handleDeleteStat = async (rowId: number) => {
        try {
            const resultAction = await dispatch(deleteAthleteSportStats(rowId));
            if (deleteAthleteSportStats.fulfilled.match(resultAction)) {
                setEditingRowId(null);
                await dispatch(fetchAthleteSportsStats({ sportId }));
            }
        } catch (error) {
            console.log(error);
        }
    };

    const handleFieldChange = (rowId: number, field: string, value: any) => {
        setEditedData((prev) => ({
            ...prev,
            [rowId]: {
                ...prev[rowId],
                [field]: value,
            },
        }));
    };

    const editStatsAPICall = async (payload, rowId) => {
        try {
            const resultAction = await dispatch(putAthleteSportStats({ payload, statId: rowId }));
            if (putAthleteSportStats.fulfilled.match(resultAction)) {
                setEditingRowId(null);
                await dispatch(fetchAthleteSportsStats({ sportId }));
            }
        } catch (error) {
            console.log(error);
        }
    }

    const handleEditSave = async (rowId: number) => {
        const updatedRow = {
            ...statsList.find((row) => row.id === rowId),
            ...editedData[rowId],
        };

        const payload = {
            stats: [
                {
                    roleId,
                    athleteId: profileId,
                    userId,
                    sportId,
                    statDate: updatedRow?.date ? format(new Date(updatedRow?.date), 'yyyy-MM-dd') : null,
                    statNameUnitId: updatedRow?.statsName?.value || '',
                    statValueNumeric: Number(updatedRow?.statsValueNumeric) || null,
                    statValueText: updatedRow?.statsValueText,
                    seasonName: updatedRow?.seasonName,
                    isHidden: updatedRow?.isPublished ? "Unhide" : "Hide",
                    isDeleted: false
                }
            ]
        };

        editStatsAPICall(payload, rowId)
    };

    const handleEditCancel = (rowId) => {
        setEditingRowId(null);
        setEditedData(prev => {
            const newData = { ...prev };
            delete newData[rowId];
            return newData;
        });
    }

    const mergedStatsList = useMemo(() => {
        return statsList?.map((row) => ({
            ...row,
            ...(editedData[row.id] ?? {})
        }));
    }, [statsList, editedData]);

    const handleFilterDate = async (date) => {
        setDate(date)
        await dispatch(fetchAthleteSportsStats({ sportId, date: date && format(new Date(date), 'yyyy-MM-dd') }));
    }
    const handleSelectPublishFilter = async (value: string) => {
        setPublishFilter(value)
        await dispatch(fetchAthleteSportsStats({ sportId, publish: value === 'publish' }));
    }

    const handleClickReset = async () => {
        setDate(undefined)
        setPublishFilter('')
        await dispatch(fetchAthleteSportsStats({ sportId }));
    }

    const columns = useMemo<ColumnDef<any>[]>(() => [
        {
            accessorKey: "seasonName",
            header: "Season Name",
            cell: (info) => {
                const row = info.row.original;
                const rowId = row.id;
                const isEditing = editingRowId === rowId;
                const value = editedData[rowId]?.seasonName ?? row.seasonName ?? "";

                return isEditing ? (
                    <Input
                        key={rowId}
                        value={value}
                        placeholder="Season Name"
                        onChange={(e) =>
                            handleFieldChange(rowId, "seasonName", e.target.value)
                        }
                        disabled={!isPremiumUser}
                    />
                ) : (
                    <span>{value || "-"}</span>
                );
            },
        },
        {
            accessorKey: "date",
            header: "Date",
            cell: (info) => {
                const row = info.row.original;
                const rowId = row.id;
                const isEditing = editingRowId === rowId;
                const value = (editedData[rowId]?.date && new Date(editedData[rowId]?.date)) ?? (row?.date && new Date(row.date));

                return isEditing ? (
                    <CommonCalender
                        key={rowId}
                        placeholder="Date"
                        dateValue={value ? new Date(value) : undefined}
                        setDateFn={(val) => handleFieldChange(rowId, "date", val)}
                        mode="single"
                        disabled={!isPremiumUser}
                    />
                ) : (
                    <span>{value ? format(new Date(value), 'MM-dd-yyyy') : "-"}</span>
                );
            },
        },
        {
            accessorKey: "statsName",
            header: "Stats Name",
            cell: (info) => {
                const row = info.row.original;
                const rowId = row.id;
                const isEditing = editingRowId === rowId;
                const value = editedData[rowId]?.statsName ?? row.statsName;

                return isEditing ? (
                    <Select
                        key={rowId}
                        value={value?.value?.toString() ?? ""}
                        onValueChange={(val) => {
                            const selected = sportStatNamesList.find(opt => opt.value.toString() === val);
                            handleFieldChange(rowId, "statsName", selected);
                        }}
                    >
                        <SelectTrigger className="border-slate-300 bg-white"
                            disabled={!isPremiumUser}
                        >
                            <SelectValue placeholder="Select Stats Name">
                                {value?.label}
                            </SelectValue>
                        </SelectTrigger>
                        <SelectContent>
                            {sportStatNamesList?.length > 0 ? sportStatNamesList?.map(item => (
                                <SelectItem key={item.value} value={item.value.toString()}>
                                    {item.label}
                                </SelectItem>
                            )) : <SelectItem disabled value="no-options">No Options Found</SelectItem>}
                        </SelectContent>
                    </Select>
                ) : (
                    <span>{value?.label || "-"}</span>
                );
            },
        },
        {
            accessorKey: "statsValueNumeric",
            header: "Stats Value Numeric",
            cell: (info) => {
                const row = info.row.original;
                const rowId = row.id;
                const isEditing = editingRowId === rowId;
                const value = editedData[rowId]?.statsValueNumeric ?? row.statsValueNumeric ?? "";

                return isEditing ? (
                    <Input
                        key={rowId}
                        value={value}
                        placeholder="Stats Value"
                        onChange={(e) =>
                            handleFieldChange(rowId, "statsValueNumeric", e.target.value)
                        }
                        disabled={!isPremiumUser}
                    />
                ) : (
                    <span>{value || "-"}</span>
                );
            },
        },
        {
            accessorKey: "statsValueText",
            header: "Stats Value Text",
            cell: (info) => {
                const row = info.row.original;
                const rowId = row.id;
                const isEditing = editingRowId === rowId;
                const value = editedData[rowId]?.statsValueText ?? row.statsValueText ?? "";

                return isEditing ? (
                    <Input
                        key={rowId}
                        value={value}
                        placeholder="Stats Value Text"
                        onChange={(e) =>
                            handleFieldChange(rowId, "statsValueText", e.target.value)
                        }
                        disabled={!isPremiumUser}
                    />
                ) : (
                    <span>{value || "-"}</span>
                );
            },
        },
        {
            accessorKey: "statsUnit",
            header: "Stats Unit",
            cell: (info) => <span>{info.getValue() as any || "-"}</span>,
        },
        {
            accessorKey: "isPublished",
            header: "Publish",
            cell: (info) => {
                const row = info.row.original;
                const rowId = row.id;
                const isEditing = editingRowId === rowId;
                const value = editedData[rowId]?.isPublished ?? row.isPublished;

                return isEditing ? (
                    <Switch
                        checked={value}
                        onCheckedChange={(val) =>
                            handleFieldChange(rowId, "isPublished", val)
                        }
                        disabled={!isPremiumUser}
                    />
                ) : (
                    <Switch
                        checked={value}
                        disabled
                    />
                );
            },
        },
        {
            accessorKey: "actions",
            header: "Actions",
            cell: (info) => {
                const rowId = info.row.original.id;
                const isEditing = editingRowId === rowId;

                return isEditing ? (
                    <div className="flex gap-2">
                        <Button disabled={!isPremiumUser} size="sm" onClick={() => handleEditSave(rowId)}>Save</Button>
                        <Button disabled={!isPremiumUser} size="sm" variant="outline" onClick={() => handleEditCancel(rowId)}>Cancel</Button>
                    </div>
                ) : (
                    <div className="flex gap-2">
                        <Button disabled={!isPremiumUser} size="icon" variant="outline" onClick={() => setEditingRowId(rowId)}>
                            <Pencil />
                        </Button>
                        <AlertPopup
                            trigger={<Button disabled={!isPremiumUser} variant="destructive" size="icon"><Trash2 /></Button>}
                            alertTitle="Confirm Deletion"
                            alertContent="Are you sure, you want to delete?"
                            action={() => handleDeleteStat(rowId)}
                        />
                    </div>
                );
            },
        },
    ], [editingRowId, sportStatNamesList]);


    return <>
        <div className="flex flex-col justify-center  gap-6 bg-slate-100 rounded-lg p-4 ">
            {!isPremiumUser && <UpgradePremiumSection />}
            <h3 className="text-xl font-bold text-center">{sportName ?? `${sportName} - `} Stats</h3>
            <div className="flex justify-end w-full">
                <Dialog open={openModal} onOpenChange={() => setOpenModal(!openModal)}>
                    <DialogTrigger asChild>
                        <Button disabled={!isPremiumUser} variant="outline" className="gap-1 font-semibold hover:text-primary">
                            <Plus />
                            Add Stats
                        </Button>
                    </DialogTrigger>
                    <DialogContent
                        onInteractOutside={(event) => event.preventDefault()}
                        className='w-[50vw]  max-w-full max-h-[90%] m-0 p-0 flex flex-col'>
                        <DialogTitle className='text-xl font-bold p-4 border-b'>
                            Add {sportName || ''} Stats
                        </DialogTitle>
                        <div className='flex-grow flex flex-col p-8'>
                            <form onSubmit={handleSubmit(onSubmit)} >
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4  overflow-y-auto">
                                    <div className="flex flex-col gap-1">
                                        <Label>Season Name</Label>
                                        <Controller
                                            name='seasonName'
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type={'text'}
                                                    placeholder='Enter Season Name'
                                                    onChange={(e) => {
                                                        const sanitizedValue = e.target.value
                                                            ?.trimStart()
                                                            ?.replace(preventSpaces, '');
                                                        field.onChange(sanitizedValue);
                                                    }}
                                                    className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                                />
                                            )}
                                        />
                                        {errors.seasonName && (
                                            <p className='text-red-500 text-sm mt-1'>
                                                {errors.seasonName.message}
                                            </p>
                                        )}
                                    </div>

                                    <div className="flex flex-col gap-1">
                                        <Label>Date</Label>
                                        <Controller
                                            name='date'
                                            control={control}
                                            render={({ field }) => (
                                                <CommonCalender
                                                    placeholder={`Date`}
                                                    mode='single'
                                                    name="date"
                                                    dateValue={field.value}
                                                    setDateFn={(date) => field.onChange(date)}
                                                />
                                            )}
                                        />
                                        {errors.date && (
                                            <p className='text-red-500 text-sm mt-1'>
                                                {errors.date.message}
                                            </p>
                                        )}
                                    </div>

                                    <div className="flex flex-col gap-1">
                                        <Label>Stats Name</Label>
                                        <Controller
                                            name='statsName'
                                            control={control}
                                            render={({ field }) => (
                                                <SearchInput
                                                    list={sportStatNamesList}
                                                    onChange={(name, selected) => {
                                                        field.onChange(selected)
                                                    }}
                                                    name="statsName"
                                                    placeholder="Select Stats Name"
                                                    value={field.value}
                                                />
                                            )}
                                        />
                                    </div>

                                    <div className="flex flex-col gap-1">
                                        <Label>Season Value Numeric</Label>
                                        <Controller
                                            name='statsValueNumeric'
                                            control={control}
                                            render={({ field }) => (
                                                <NumericInput
                                                    {...field}
                                                    placeholder='Enter Stats Value Numeric'
                                                    onChange={(e) => {
                                                        const sanitizedValue = e.target.value
                                                            ?.trimStart()
                                                            ?.replace(preventSpaces, '');
                                                        field.onChange(sanitizedValue);
                                                    }}
                                                    className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                                />
                                            )}
                                        />
                                    </div>

                                    <div className="flex flex-col gap-1">
                                        <Label>Stats Value Text</Label>
                                        <Controller
                                            name='statsValueText'
                                            control={control}
                                            render={({ field }) => (
                                                <Input
                                                    {...field}
                                                    type={'text'}
                                                    placeholder='Enter Stats Value Text'
                                                    onChange={(e) => {
                                                        const sanitizedValue = e.target.value
                                                            ?.trimStart()
                                                            ?.replace(preventSpaces, '');
                                                        field.onChange(sanitizedValue);
                                                    }}
                                                    value={field.value}
                                                    className='mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-indigo-300 focus:ring focus:ring-indigo-200 focus:ring-opacity-50'
                                                />
                                            )}
                                        />
                                    </div>

                                    <div className="flex items-center gap-2">
                                        <Label>Is Publish</Label>
                                        <Controller
                                            name='isPublished'
                                            control={control}
                                            render={({ field }) => (
                                                <Switch
                                                    checked={field.value}
                                                    onCheckedChange={field.onChange}
                                                />
                                            )}
                                        />
                                    </div>
                                </div>

                                <div className='flex flex-row justify-end mt-4 gap-5'>
                                    <Button variant={'outline'} className='w-24 border-primary text-primary hover:text-primary' type='button' onClick={() => setOpenModal(false)}>
                                        Cancel
                                    </Button>
                                    <Button className='w-24' type='submit'>
                                        {apiStatus === 'sportStatsPending' ? (
                                            <Loader className='mr-2 h-4 w-4 animate-spin' />
                                        ) : (
                                            'Save'
                                        )}
                                    </Button>
                                </div>
                            </form>
                        </div>
                    </DialogContent>
                </Dialog>
            </div>

            <div className={`grid grid-cols-1 ${(date || publishFilter) ? 'md:grid-cols-7' : 'md:grid-cols-2'} items-end gap-6 w-full`}>
                <div className={`flex flex-col gap-1 ${date || publishFilter ? 'md:col-span-3' : ''}`}>
                    <Label>Date</Label>
                    <CommonCalender
                        placeholder="Filter Date"
                        dateValue={date}
                        setDateFn={handleFilterDate}
                        mode="single"
                    />
                </div>
                <div className={`flex flex-col gap-1 ${date || publishFilter ? 'md:col-span-3' : ''}`}>
                    <Label>Publish</Label>
                    <Select onValueChange={handleSelectPublishFilter} value={publishFilter || ''}>
                        <SelectTrigger className="border-slate-300 bg-white">
                            <SelectValue placeholder="Filter Publish" />
                        </SelectTrigger>
                        <SelectContent>
                            <SelectItem value="publish">Publish</SelectItem>
                            <SelectItem value="unPublish">Un Publish</SelectItem>
                        </SelectContent>
                    </Select>
                </div>
                {(date || publishFilter) && <Button size={'icon'} variant={'outline'} onClick={handleClickReset}><RiResetLeftLine /></Button>}
            </div>

            <GlobalSortPaginationTable
                data={mergedStatsList}
                columns={columns}
            />
        </div>
    </>
}
export default React.memo(SportStats)