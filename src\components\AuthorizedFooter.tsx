'use client'
import { useTokenValues } from "@/hooks/useTokenValues";
import { usePathname } from "next/navigation";

const AuthorizedFooter = () => {
    const { roleId } = useTokenValues()
    const pathname = usePathname()

    return (
        <>
            {roleId && pathname !== '/' && (
                <>
                    <div className="pt-4 w-full flex items-center justify-center bg-white p-4 mt-5">
                        <p className="text-center max-w-7xl lg:px-14 font-semibold">
                            © 2025 Connect Athlete. All rights reserved.
                            Connect Athlete™, SPARQX™, SAAR™, and all related technologies are the
                            intellectual property of Connect Athlete and protected by copyright
                            and trademark laws.
                        </p>
                    </div>
                </>
            )}
        </>
    );
}
export default AuthorizedFooter