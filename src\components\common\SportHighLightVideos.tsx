'use client'
import VideoUploader from "@/components/common/VideoUploader"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import {
    <PERSON><PERSON>,
    <PERSON><PERSON><PERSON>ontent,
    <PERSON><PERSON>Footer,
    <PERSON><PERSON>Header,
    <PERSON><PERSON>Title,
    DialogTrigger
} from "@/components/ui/dialog"
import { useTokenValues } from "@/hooks/useTokenValues"
import { ROLES } from "@/utils/constants"
import { Loader, PencilLine, Plus, Trash2 } from "lucide-react"
import { Params } from "next/dist/shared/lib/router/utils/route-matcher"
import { useEffect, useState } from "react"
import { Input } from "../ui/input"
import { Separator } from "../ui/separator"
import { Switch } from "../ui/switch"
import AlertPopup from "./AlertPopup"
import UpgradePremiumSection from "./UpgradePremiumSection"

interface IProps {
    params: Params;
    loading?: boolean;
    toggleVideoSection: boolean;
    latestVideoData: any;
    addedHighLightVideoList: any[];
    handleUpdateLatestVideoValues: (name: string, value: string | File | null | boolean) => void;
    handleSaveHighlightVideo: () => void;
    handleEditHighLightVideo: (id: number) => void;
    handleDeleteHighLightVideo: (id: number) => void;
    openModal?: boolean;
    handleModal?: () => void;
    isEdit?: boolean
}
const SportHighLightVideos = ({
    params,
    loading,
    toggleVideoSection,
    latestVideoData,
    addedHighLightVideoList,
    openModal,
    isEdit,
    handleModal,
    handleUpdateLatestVideoValues,
    handleSaveHighlightVideo,
    handleEditHighLightVideo,
    handleDeleteHighLightVideo
}: IProps) => {
    const [addedVideosList, setAddedVideoList] = useState(addedHighLightVideoList)
    const decodedSportName = decodeURIComponent(params?.sportName);
    const { isPremiumUser, roleId } = useTokenValues()
    const isAthletePremiumUser = roleId === ROLES.ATHLETE && isPremiumUser

    useEffect(() => {
        addedHighLightVideoList && setAddedVideoList(addedHighLightVideoList)
    }, [addedHighLightVideoList])

    return (
        <>
            <div className="bg-slate-100 rounded-lg p-4 space-y-5">
                {!isAthletePremiumUser && <UpgradePremiumSection />}
                <div className="flex flex-col justify-center gap-1">
                    <div className="flex flex-wrap items-center justify-center gap-5">
                        <h3 className="text-xl font-bold text-center">{decodedSportName ?? `${decodedSportName} - `}  Latest Highlight Videos</h3>
                        <Switch
                            checked={toggleVideoSection}
                            onCheckedChange={(checked) => handleUpdateLatestVideoValues('toggleVideoSection', checked)}
                            disabled={!isAthletePremiumUser}
                        />
                    </div>
                    <p className="text-secondary text-center text-md">{isAthletePremiumUser ? 'Enjoy uploading up to 20 videos!' : 'You can share up to 6 videos.'}</p>
                </div>
                <Dialog open={openModal} onOpenChange={handleModal}>
                    <DialogTrigger asChild disabled={!isAthletePremiumUser || (isAthletePremiumUser ? addedHighLightVideoList?.length >= 20 : addedHighLightVideoList?.length >= 6)}>
                        <div className="flex justify-end">
                            <Button variant={'outline'} className="self-end" disabled={!isAthletePremiumUser}>
                                <Plus /> Add Video
                            </Button>
                        </div>
                    </DialogTrigger>
                    <DialogContent onInteractOutside={(event) => event.preventDefault()}>
                        <DialogHeader>
                            <DialogTitle>{isEdit ? 'Update' : 'Add'} HighLight Video</DialogTitle>
                        </DialogHeader>
                        <div className="flex flex-col gap-4 overflow-y-auto sm:max-w-[600px] p-3 max-h-[65vh]">
                            <div className="grid grid-cols-1 items-center mt-5 gap-3">
                                <div>
                                    <Input
                                        placeholder="Video Title"
                                        value={latestVideoData?.title}
                                        onChange={(e) => handleUpdateLatestVideoValues('title', e.target.value)}
                                        name="title"
                                        disabled={!isAthletePremiumUser}
                                    />
                                    <p className="text-xs text-destructive text-right">Max 75 chars.</p>
                                </div>

                                <div>
                                    <Input
                                        placeholder="About Video"
                                        value={latestVideoData?.aboutVideo}
                                        onChange={(e) => handleUpdateLatestVideoValues('aboutVideo', e.target.value)}
                                        name="aboutVideo"
                                        maxLength={100}
                                        disabled={!isAthletePremiumUser}
                                    />
                                    <p className="text-xs text-destructive text-right">Max 100 chars.</p>
                                </div>
                            </div>

                            <div className="flex items-center justify-center w-full">
                                <VideoUploader
                                    value={latestVideoData?.video}
                                    handleAdd={(file) => handleUpdateLatestVideoValues('video', file)}
                                    name="uploadLatestVideo"
                                    className="w-full h-48"
                                    handleRemove={() => handleUpdateLatestVideoValues('video', null)}
                                    disabled={!isAthletePremiumUser}
                                />
                            </div>
                        </div>

                        <DialogFooter>
                            <Button disabled={loading || !isAthletePremiumUser} className="self-end w-20" onClick={handleSaveHighlightVideo}>
                                {loading ?
                                    <Loader className="animate-spin text-white w-10 h-10" />
                                    : 'Save'}
                            </Button>
                        </DialogFooter>
                    </DialogContent>
                </Dialog>

                {toggleVideoSection ?
                    <div className="w-full overflow-x-auto">
                        <div className="flex gap-5 p-2 min-w-max">
                            {addedVideosList?.length > 0 ? (
                                addedVideosList.map((item) => (
                                    <Card key={item.id} className="w-[400px] h-[400px] flex-shrink-0 flex flex-col justify-between p-4 gap-0 rounded-lg">
                                        <CardHeader className="p-0 text-center">
                                            <CardTitle className="text-base line-clamp-2">{item?.title}</CardTitle>
                                        </CardHeader>

                                        <CardContent className="p-0 flex-1 flex flex-col justify-center items-center">
                                            <div className="w-full flex flex-col justify-center items-center gap-4">
                                                {item?.video && (
                                                    <video
                                                        src={item?.video || ''}
                                                        controls
                                                        className="w-full h-[200px] object-cover rounded-lg border"
                                                    />
                                                )}
                                                <p className="text-sm text-center line-clamp-2 px-2">{item?.aboutVideo}</p>
                                            </div>
                                            <Separator className="bg-slate-300 my-2 w-full" />
                                        </CardContent>

                                        <CardFooter className="flex items-center justify-between w-full gap-4 p-0">
                                            <Button
                                                size={'icon'}
                                                variant={'outline'}
                                                onClick={() => handleEditHighLightVideo(item?.id)}
                                            >
                                                <PencilLine />
                                            </Button>
                                            <AlertPopup
                                                trigger={
                                                    <Button variant={'destructive'} size={'icon'}>
                                                        <Trash2 />
                                                    </Button>
                                                }
                                                alertTitle="Confirm Deletion"
                                                alertContent="Are you sure you want to delete this video?"
                                                action={() => handleDeleteHighLightVideo(item?.id)}
                                            />
                                        </CardFooter>
                                    </Card>
                                ))
                            ) : (
                                <div className="flex items-center justify-center py-5">
                                    <p className="text-center text-gray-600 text-sm w-3/4">
                                        Looks like there are no {decodedSportName} highlight videos yet. Be the first to showcase your talent — upload a video now!
                                    </p>
                                </div>
                            )}
                        </div>
                    </div>
                    :
                    null}
            </div >
        </>
    )
}
export default SportHighLightVideos