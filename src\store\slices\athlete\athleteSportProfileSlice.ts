import axiosInstance from "@/utils/axiosInstance";
import { AthleteSportProfileStates } from "@/utils/interfaces";
import { createAsyncThunk, createSlice } from "@reduxjs/toolkit";
import axios from "axios";
import { toast } from "react-toastify";

const initialState: AthleteSportProfileStates = {
  apiStatus: "",
  error: "",
  sportFormData: {
    selectedSport: null,
    isPrimarySport: false,
    yearsPlayed: "",
    currentSeason: "",
    selectedSportLevel: null,
    addedSportSpecilitiesList: [],
    currentTeam: "",
  },
  statsFormData: null,
  statsList: [],
  toggleVideoSection: true,
  toggleHighlightLinks: true,
  isEditHighlightLinks: false,
  latestVideoData: {
    title: "",
    aboutVideo: "",
    video: null,
  },
  highlightLinksList: [],
  addedHighlightVideoList: [],
  toggleMileStone: true,
  isAddMileStone: false,
  mileStoneData: null,
  addedMileStonesList: [],
  toggleVictoryVault: true,
  isVictoryVault: false,
  vicotryVaultData: null,
  addedVictoryVaultList: [],
  vaultId: null,
  mileStoneId: null,
  videoModalOpen: false,
  videoId: null,
  isSportProfileHide: false,
  publicAthleteSportToggles: null,
};

export const postAthleteSportInfo = createAsyncThunk(
  "athleteSportInfo/postAthleteSportInfo",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsIntro/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const putAthleteSportInfo = createAsyncThunk(
  "athleteSportInfo/putAthleteSportInfo",
  async ({ payload, sportId }: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsIntro/${userId}/${sportId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchAthleteSportsByUserSportId = createAsyncThunk(
  "athleteSportInfo/fetchAthleteSportsByUserSportId",
  async (sportId: number, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsIntro/${userId}/${sportId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const postAthleteSportStats = createAsyncThunk(
  "athleteSportStats/postAthleteSportStats",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsStats`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const putAthleteSportStats = createAsyncThunk(
  "athleteSportStats/putAthleteSportStats",
  async ({ payload, statId }: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsStats/${statId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchAthleteSportsStats = createAsyncThunk(
  "athleteSportStats/fetchAthleteSportsStats",
  async (
    {
      sportId,
      date,
      publish,
    }: { sportId: number; date?: string; publish?: boolean },
    { fulfillWithValue, rejectWithValue }
  ) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        date || publish
          ? `${
              process.env.NEXT_PUBLIC_USERPROFILE_API_URL
            }/athleteSportsStats/${userId}/${sportId}?date=${
              date || ""
            }&publish=${publish}`
          : `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsStats/${userId}/${sportId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const deleteAthleteSportStats = createAsyncThunk(
  "athleteSportStats/deleteAthleteSportStats",
  async (statId: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsStats/${statId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const postAthleteSportVideos = createAsyncThunk(
  "athleteSportVideos/postAthleteSportVideos",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportHighlightsVideo`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchAthleteSportVideos = createAsyncThunk(
  "athleteSportVideos/fetchAthleteSportVideos",
  async (sportId: number, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportHighlightsVideo/${userId}/${sportId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(
          error.response.data?.message ||
            error?.response?.data?.errors?.join(",") ||
            "Server error"
        );
      }
    }
  }
);

export const putAthleteSportVideos = createAsyncThunk(
  "athleteSportVideos/putAthleteSportVideos",
  async ({ payload, videoId }: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportHighlightsVideo/${videoId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const deleteAthleteSportVideos = createAsyncThunk(
  "athleteSportVideos/deleteAthleteSportVideos",
  async (videoId: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportHighlightsVideo/${videoId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const postAthleteSportLinks = createAsyncThunk(
  "athleteSportLinks/postAthleteSportLinks",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportHighlightsLinks`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchAthleteSportLinks = createAsyncThunk(
  "athleteSportLinks/fetchAthleteSportLinks",
  async (sportId: number, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportHighlightsLinks/${userId}/${sportId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(
          error.response.data?.message ||
            error?.response?.data?.errors?.join(",") ||
            "Server error"
        );
      }
    }
  }
);

export const putAthleteSportLinks = createAsyncThunk(
  "athleteSportLinks/putAthleteSportLinks",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportHighlightsLinks/${userId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const deleteAthleteSportLinks = createAsyncThunk(
  "athleteSportLinks/deleteAthleteSportLinks",
  async (linkId: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportHighlightsLinks/${linkId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const postAthleteSportMileStones = createAsyncThunk(
  "athleteSportMileStones/postAthleteSportMileStones",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsMilestones`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchAthleteSportMilestones = createAsyncThunk(
  "athleteSportMilestones/fetchAthleteSportMilestones",
  async (sportId: number, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsMilestones/${userId}/${sportId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(
          error.response.data?.message ||
            error?.response?.data?.errors?.join(",") ||
            "Server error"
        );
      }
    }
  }
);

export const putAthleteSportMilestones = createAsyncThunk(
  "athleteSportMilestones/putAthleteSportMilestones",
  async (
    { payload, mileStoneId }: any,
    { fulfillWithValue, rejectWithValue }
  ) => {
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsMilestones/${mileStoneId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const deleteAthleteSportMilestone = createAsyncThunk(
  "athleteSportMilestne/deleteAthleteSportMilestone",
  async (linkId: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsMilestones/${linkId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const postAthleteSportVictoryVault = createAsyncThunk(
  "athleteSportVictoryVault/postAthleteSportVictoryVault",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteVictoryVault`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchAthleteSportVictoryVault = createAsyncThunk(
  "athleteSportInfo/fetchAthleteSportVictoryVault",
  async (sportId: number, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteVictoryVault/${userId}/${sportId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (error.response) {
        console.error("Server Error:", error.response);
        return rejectWithValue(error.response.data?.message || "Server error");
      }
    }
  }
);

export const putAthleteSportVictoryVault = createAsyncThunk(
  "athleteSportVictoryVault/putAthleteSportVictoryVault",
  async ({ payload, vaultId }: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteVictoryVault/${vaultId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const deleteAthleteSportVictoryVault = createAsyncThunk(
  "athleteSportVictoryVault/deleteAthleteSportVictoryVault",
  async (linkId: any, { fulfillWithValue, rejectWithValue }) => {
    try {
      const response = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteVictoryVault/${linkId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const putAthleteSportProfileHide = createAsyncThunk(
  "athleteSportProfileHide/putAthleteSportProfileHide",
  async ({ payload, sportId }: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.put(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsPageUrl/${userId}/${sportId}`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchAthleteSportProfileHide = createAsyncThunk(
  "athleteSportProfileHide/fetchAthleteSportProfileHide",
  async (sportId: number, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athleteSportsPageUrl/${userId}/${sportId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
    }
  }
);

export const deleteAthleteSport = createAsyncThunk(
  "athleteSportProfileHide/deleteAthleteSport",
  async (sportId: number, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.delete(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/deleteSport/${userId}/${sportId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const postAthleteSportsSectionHide = createAsyncThunk(
  "athleteSportSectionsHide/postAthleteSportsSectionHide",
  async (payload: any, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.post(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/sportExposeSection`,
        payload
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchAthleteSportSectionsHide = createAsyncThunk(
  "athleteSportSectionsHide/fetchAthleteSportSectionsHide",
  async (sportId: number, { fulfillWithValue, rejectWithValue }) => {
    const userId = localStorage.getItem("userId");
    try {
      const response = await axiosInstance.get(
        `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/sportExposeSection/${userId}/${sportId}`
      );

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue("Network error or no response");
      }
    }
  }
);

const proxyUrl = `${process.env.NEXT_PUBLIC_USERPROFILE_API_URL}/athlete/proxy`;

export const fetchPublicAthleteSportIntro = createAsyncThunk(
  "athletePublicSportIntro/fetchPublicAthleteSportIntro",
  async (
    { userId, sportId }: { userId: number; sportId: number },
    { fulfillWithValue, rejectWithValue }
  ) => {
    try {
      const response = await axios(`${proxyUrl}/${userId}/${sportId}`, {
        headers: {
          method: "athleteSportsIntro",
          "x-api-key": `${process.env.NEXT_PUBLIC_PROXY_API_KEY}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchPublicAthleteSportStats = createAsyncThunk(
  "athletePublicSportStats/fetchPublicAthleteSportStats",
  async (
    { userId, sportId }: { userId: number; sportId: number },
    { fulfillWithValue, rejectWithValue }
  ) => {
    try {
      const response = await axios(`${proxyUrl}/${userId}/${sportId}`, {
        headers: {
          method: "athleteSportsStats",
          "x-api-key": `${process.env.NEXT_PUBLIC_PROXY_API_KEY}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchPublicAthleteSportVideos = createAsyncThunk(
  "athletePublicSportVideos/fetchPublicAthleteSportVideos",
  async (
    { userId, sportId }: { userId: number; sportId: number },
    { fulfillWithValue, rejectWithValue }
  ) => {
    try {
      const response = await axios(`${proxyUrl}/${userId}/${sportId}`, {
        headers: {
          method: "athleteHighlightVideos",
          "x-api-key": `${process.env.NEXT_PUBLIC_PROXY_API_KEY}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchPublicAthleteSportLinks = createAsyncThunk(
  "athletePublicSportLinks/fetchPublicAthleteSportLinks",
  async (
    { userId, sportId }: { userId: number; sportId: number },
    { fulfillWithValue, rejectWithValue }
  ) => {
    try {
      const response = await axios(`${proxyUrl}/${userId}/${sportId}`, {
        headers: {
          method: "athleteHighlightLinkById",
          "x-api-key": `${process.env.NEXT_PUBLIC_PROXY_API_KEY}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchPublicAthleteSportMilestones = createAsyncThunk(
  "athletePublicSportMilestones/fetchPublicAthleteSportMilestones",
  async (
    { userId, sportId }: { userId: number; sportId: number },
    { fulfillWithValue, rejectWithValue }
  ) => {
    try {
      const response = await axios(`${proxyUrl}/${userId}/${sportId}`, {
        headers: {
          method: "athleteMilestones",
          "x-api-key": `${process.env.NEXT_PUBLIC_PROXY_API_KEY}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchPublicAthleteSportVictoryVault = createAsyncThunk(
  "athletePublicSportVictoryVault/fetchPublicAthleteSportVictoryVault",
  async (
    { userId, sportId }: { userId: number; sportId: number },
    { fulfillWithValue, rejectWithValue }
  ) => {
    try {
      const response = await axios(`${proxyUrl}/${userId}/${sportId}`, {
        headers: {
          method: "athleteVictoryVault",
          "x-api-key": `${process.env.NEXT_PUBLIC_PROXY_API_KEY}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

export const fetchPublicAthleteSportExpose = createAsyncThunk(
  "athletePublicSportExpose/fetchPublicAthleteSportExpose",
  async (
    { userId, sportId }: { userId: number; sportId: number },
    { fulfillWithValue, rejectWithValue }
  ) => {
    try {
      const response = await axios(`${proxyUrl}/${userId}/${sportId}`, {
        headers: {
          method: "athleteSportExpose",
          "x-api-key": `${process.env.NEXT_PUBLIC_PROXY_API_KEY}`,
          Accept: "application/json",
          "Content-Type": "application/json",
        },
      });

      if (response.status === 200 || response.status === 201) {
        return fulfillWithValue(response.data.data || response.data);
      } else {
        return rejectWithValue(response.data.message || "Something went wrong");
      }
    } catch (error: any) {
      if (axios.isAxiosError(error)) {
        return rejectWithValue(
          error?.response?.data?.message || "Network error or no response"
        );
      }
    }
  }
);

const athleteSportProfileSlice = createSlice({
  name: "athleteSportProfile",
  initialState,
  reducers: {
    handleUpdateUserInput: (state, action) => {
      const { name, value } = action.payload;
      state[name] = value;
    },
  },
  extraReducers(builder) {
    builder
      .addCase(postAthleteSportInfo.pending, (state) => {
        state.apiStatus = "sportInfoPending";
      })
      .addCase(postAthleteSportInfo.fulfilled, (state) => {
        state.apiStatus = "sportInfoSuccess";
        toast.success("Sport added successfully!");
      })
      .addCase(postAthleteSportInfo.rejected, (state, action) => {
        state.apiStatus = "sportInfoReject";
        state.error = action.payload as string;
        toast.error((action.payload as string) || "Failed to add sport");
      })
      .addCase(putAthleteSportInfo.pending, (state) => {
        state.apiStatus = "sportInfoPending";
      })
      .addCase(putAthleteSportInfo.fulfilled, (state) => {
        state.apiStatus = "sportInfoSuccess";
        toast.success("Sport updated successfully!");
      })
      .addCase(putAthleteSportInfo.rejected, (state, action) => {
        state.apiStatus = "sportInfoReject";
        state.error = action.payload as string;
        toast.error((action.payload as string) || "Failed to update sport");
      })
      .addCase(fetchAthleteSportsByUserSportId.fulfilled, (state, action) => {
        state.apiStatus = "";
        if (action.payload) {
          const {
            currentSeasonStatus,
            currentTeam,
            level,
            primarySport,
            specialities,
            sportId,
            sportName,
            yearsPlayed,
          } = action.payload;
          const formattedSportData = {
            selectedSport: { value: sportId, label: sportName },
            isPrimarySport: primarySport,
            yearsPlayed: yearsPlayed?.toString(),
            currentSeason: currentSeasonStatus,
            selectedSportLevel: {
              value: level?.id,
              label: level?.levelName,
            },
            addedSportSpecilitiesList: specialities?.map((each) => ({
              value: each?.id,
              label: each?.specialityName,
            })),
            currentTeam: currentTeam,
          };
          state.sportFormData = formattedSportData;
        }
      })
      .addCase(fetchAthleteSportsByUserSportId.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
        state.sportFormData = null;
      })
      .addCase(postAthleteSportStats.pending, (state) => {
        state.apiStatus = "sportStatsPending";
      })
      .addCase(postAthleteSportStats.fulfilled, (state) => {
        state.apiStatus = "sportStatsSuccess";
        toast.success("Sport stats added successfully!");
      })
      .addCase(postAthleteSportStats.rejected, (state, action) => {
        state.apiStatus = "sportStatsReject";
        state.error = action.payload as string;
        toast.error((action.payload as string) || "Failed to add sport stats");
      })
      .addCase(putAthleteSportStats.pending, (state) => {
        state.apiStatus = "sportStatsPending";
      })
      .addCase(putAthleteSportStats.fulfilled, (state) => {
        state.apiStatus = "sportStatsSuccess";
        toast.success("Sport stats updated successfully!");
      })
      .addCase(putAthleteSportStats.rejected, (state, action) => {
        state.apiStatus = "sportStatsReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update sport stats"
        );
      })
      .addCase(fetchAthleteSportsStats.fulfilled, (state, action) => {
        state.apiStatus = "";
        const formattedList = action.payload?.map((each) => ({
          id: each?.id,
          seasonName: each?.seasonName,
          date: each?.statDate,
          statsName: {
            value: each?.statNameUnit?.id,
            label: each?.statNameUnit?.statName,
          },
          statsValueNumeric: each?.statValueNumeric,
          statsValueText: each?.statValueText,
          statsUnit: each?.statNameUnit?.statUnit,
          isPublished: each?.isHidden === "Unhide",
        }));
        state.statsList = formattedList;
      })
      .addCase(fetchAthleteSportsStats.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
        state.statsList = [];
      })
      .addCase(deleteAthleteSportStats.pending, (state) => {
        state.apiStatus = "delSportStatsPending";
      })
      .addCase(deleteAthleteSportStats.fulfilled, (state) => {
        state.apiStatus = "delSportStatsSuccess";
        toast.success("Sport stats deleted successfully!");
      })
      .addCase(deleteAthleteSportStats.rejected, (state, action) => {
        state.apiStatus = "delSportStatsReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to delete sport stats"
        );
      })
      .addCase(postAthleteSportVideos.pending, (state) => {
        state.apiStatus = "sportVideoPending";
      })
      .addCase(postAthleteSportVideos.fulfilled, (state) => {
        state.apiStatus = "sportVideoSuccess";
        toast.success("Highlight video added successfully!");
      })
      .addCase(postAthleteSportVideos.rejected, (state, action) => {
        state.apiStatus = "sportVideoReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to add highlight video"
        );
      })
      .addCase(fetchAthleteSportVideos.fulfilled, (state, action) => {
        state.apiStatus = "";
        if (action.payload) {
          const formattedList = action.payload?.map((each) => ({
            id: each?.id,
            title: each?.videotitle,
            aboutVideo: each?.aboutVideo,
            video: each?.videoUrl,
          }));
          state.addedHighlightVideoList = formattedList;
        }
      })
      .addCase(fetchAthleteSportVideos.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
        state.addedHighlightVideoList = [];
      })
      .addCase(putAthleteSportVideos.pending, (state) => {
        state.apiStatus = "sportVideoPending";
      })
      .addCase(putAthleteSportVideos.fulfilled, (state) => {
        state.apiStatus = "sporVideoSuccess";
        toast.success("Highlight video updated successfully!");
      })
      .addCase(putAthleteSportVideos.rejected, (state, action) => {
        state.apiStatus = "sporVideoReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update sport video"
        );
      })
      .addCase(deleteAthleteSportVideos.pending, (state) => {
        state.apiStatus = "delSportVideoPending";
      })
      .addCase(deleteAthleteSportVideos.fulfilled, (state) => {
        state.apiStatus = "delSportVideoSuccess";
        toast.success("Video deleted successfully!");
      })
      .addCase(deleteAthleteSportVideos.rejected, (state, action) => {
        state.apiStatus = "delSportVideoReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to delete sport video"
        );
      })
      .addCase(postAthleteSportLinks.pending, (state) => {
        state.apiStatus = "athleteSportLinkPending";
      })
      .addCase(postAthleteSportLinks.fulfilled, (state) => {
        state.apiStatus = "athleteSportLinkSuccess";
        toast.success("Highlight link added successfully!");
      })
      .addCase(postAthleteSportLinks.rejected, (state, action) => {
        state.apiStatus = "athleteSportLinkReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to add highlight link"
        );
      })
      .addCase(fetchAthleteSportLinks.fulfilled, (state, action) => {
        state.apiStatus = "";
        if (action.payload) {
          state.highlightLinksList = action.payload?.map((each) => ({
            id: each?.id,
            text: each?.highlightText,
            url: each?.highlightUrl,
          }));
        }
      })
      .addCase(fetchAthleteSportLinks.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
        state.highlightLinksList = [];
      })
      .addCase(putAthleteSportLinks.pending, (state) => {
        state.apiStatus = "athleteSportLinkPending";
      })
      .addCase(putAthleteSportLinks.fulfilled, (state) => {
        state.apiStatus = "sporLinksSuccess";
        toast.success("Link updated successfully!");
      })
      .addCase(putAthleteSportLinks.rejected, (state, action) => {
        state.apiStatus = "sporLinksReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update sport link"
        );
      })
      .addCase(deleteAthleteSportLinks.pending, (state) => {
        state.apiStatus = "delSportLinkPending";
      })
      .addCase(deleteAthleteSportLinks.fulfilled, (state) => {
        state.apiStatus = "delSportLinkSuccess";
        toast.success("Link deleted successfully!");
      })
      .addCase(deleteAthleteSportLinks.rejected, (state, action) => {
        state.apiStatus = "delSportLinkReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to delete sport link"
        );
      })

      .addCase(postAthleteSportMileStones.pending, (state) => {
        state.apiStatus = "sportMilestonePending";
      })
      .addCase(postAthleteSportMileStones.fulfilled, (state) => {
        state.apiStatus = "sportMilestoneSuccess";
        toast.success("Milestone added successfully!");
      })
      .addCase(postAthleteSportMileStones.rejected, (state, action) => {
        state.apiStatus = "sportMilestoneReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to add sport milestone"
        );
      })
      .addCase(fetchAthleteSportMilestones.fulfilled, (state, action) => {
        state.apiStatus = "";
        if (action.payload) {
          const formattedList = action.payload?.map((each) => ({
            id: each?.id,
            date: each?.milestoneDate,
            title: each?.milestoneTitle,
            link: each?.milestoneUrl,
            blurb: each?.milestoneBlurb,
            tags: each?.generalTags?.map((each) => ({
              value: each?.tagId,
              label: each?.tagValue,
            })),
            file: each?.mediaUrl,
          }));
          state.addedMileStonesList = formattedList;
        }
      })
      .addCase(fetchAthleteSportMilestones.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
        state.addedMileStonesList = [];
      })
      .addCase(putAthleteSportMilestones.pending, (state) => {
        state.apiStatus = "sportMilestonePending";
      })
      .addCase(putAthleteSportMilestones.fulfilled, (state) => {
        state.apiStatus = "sporMilestoneSuccess";
        toast.success("Milestone updated successfully!");
      })
      .addCase(putAthleteSportMilestones.rejected, (state, action) => {
        state.apiStatus = "sporMilestoneReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update sport milestone"
        );
      })
      .addCase(deleteAthleteSportMilestone.pending, (state) => {
        state.apiStatus = "delSportMilestonePending";
      })
      .addCase(deleteAthleteSportMilestone.fulfilled, (state) => {
        state.apiStatus = "delSportMilestoneSuccess";
        toast.success("Milestone deleted successfully!");
      })
      .addCase(deleteAthleteSportMilestone.rejected, (state, action) => {
        state.apiStatus = "delSportMilestoneReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to delete sports milestone"
        );
      })

      .addCase(postAthleteSportVictoryVault.pending, (state) => {
        state.apiStatus = "victoryPending";
      })
      .addCase(postAthleteSportVictoryVault.fulfilled, (state) => {
        state.apiStatus = "victorySuccess";
        toast.success("Vicotry vault added successfully!");
      })
      .addCase(postAthleteSportVictoryVault.rejected, (state, action) => {
        state.apiStatus = "victoryFailed";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to add vicotry vault sports"
        );
      })
      .addCase(fetchAthleteSportVictoryVault.pending, (state, action) => {
        state.apiStatus = "fetchVicotryPending";
      })
      .addCase(fetchAthleteSportVictoryVault.fulfilled, (state, action) => {
        state.apiStatus = "fetchVicotrySuccess";
        if (action.payload) {
          const formattedList = action.payload?.map((each) => ({
            id: each?.id,
            date: each?.achievementDate
              ? new Date(each?.achievementDate)
              : undefined,
            title: each?.achievementTitle,
            link: each?.achievementUrl,
            blurb: each?.achievementBlurb,
            tags: each?.generalTags?.map((each) => ({
              value: each?.tagId,
              label: each?.tagValue,
            })),
            file: each?.mediaUrl,
          }));
          state.addedVictoryVaultList = formattedList;
        }
      })
      .addCase(fetchAthleteSportVictoryVault.rejected, (state, action) => {
        state.apiStatus = "fetchVicotryFailed";
        state.error = action.payload as string;
        state.addedVictoryVaultList = [];
      })
      .addCase(putAthleteSportVictoryVault.pending, (state) => {
        state.apiStatus = "victoryPending";
      })
      .addCase(putAthleteSportVictoryVault.fulfilled, (state) => {
        state.apiStatus = "victorytSuccess";
        toast.success("Victory vault updated successfully!");
      })
      .addCase(putAthleteSportVictoryVault.rejected, (state, action) => {
        state.apiStatus = "victoryReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to update sports milestone"
        );
      })
      .addCase(deleteAthleteSportVictoryVault.pending, (state) => {
        state.apiStatus = "delSportVictoryVaultPending";
      })
      .addCase(deleteAthleteSportVictoryVault.fulfilled, (state) => {
        state.apiStatus = "delSportVictoryVaultSuccess";
        toast.success("Victory vault deleted successfully!");
      })
      .addCase(deleteAthleteSportVictoryVault.rejected, (state, action) => {
        state.apiStatus = "delSportVictoryVaultReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) || "Failed to delete sports victory vault"
        );
      })
      .addCase(fetchAthleteSportProfileHide.pending, (state, action) => {
        state.apiStatus = "";
      })
      .addCase(fetchAthleteSportProfileHide.fulfilled, (state, action) => {
        state.apiStatus = "";
        if (action.payload) {
          state.isSportProfileHide = !action.payload?.hideSportsProfileUrl;
        }
      })
      .addCase(fetchAthleteSportProfileHide.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
        state.isSportProfileHide = true;
      })
      .addCase(putAthleteSportProfileHide.pending, (state) => {
        state.apiStatus = "profileHidePending";
      })
      .addCase(putAthleteSportProfileHide.fulfilled, (state) => {
        state.apiStatus = "profileHideSuccess";
        toast.success("Sport profile visibility updated successfully!");
      })
      .addCase(putAthleteSportProfileHide.rejected, (state, action) => {
        state.apiStatus = "profileHideReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) ||
            "Failed to update sport profile visibility"
        );
      })
      .addCase(deleteAthleteSport.pending, (state) => {
        state.apiStatus = "delSportPending";
      })
      .addCase(deleteAthleteSport.fulfilled, (state) => {
        state.apiStatus = "delSportSuccess";
        toast.success("Sport deleted successfully!");
      })
      .addCase(deleteAthleteSport.rejected, (state, action) => {
        state.apiStatus = "delSportReject";
        state.error = action.payload as string;
        toast.error((action.payload as string) || "Failed to delete sport");
      })
      .addCase(postAthleteSportsSectionHide.pending, (state) => {
        state.apiStatus = "sectionsHidePending";
      })
      .addCase(postAthleteSportsSectionHide.fulfilled, (state) => {
        state.apiStatus = "sectionsHideSuccess";
        toast.success("Sport features hide/unhide updated successfully!");
      })
      .addCase(postAthleteSportsSectionHide.rejected, (state, action) => {
        state.apiStatus = "sectionsHideReject";
        state.error = action.payload as string;
        toast.error(
          (action.payload as string) ||
            "Failed to update sport features hide/unhide"
        );
      })
      .addCase(fetchAthleteSportSectionsHide.pending, (state, action) => {
        state.apiStatus = "";
      })
      .addCase(fetchAthleteSportSectionsHide.fulfilled, (state, action) => {
        state.apiStatus = "";
        if (action.payload) {
          const { highlightLinks, highlightVideos, milestones, victoryVault } =
            action.payload;
          state.toggleHighlightLinks = highlightLinks;
          state.toggleVideoSection = highlightVideos;
          state.toggleMileStone = milestones;
          state.toggleVictoryVault = victoryVault;
        }
      })
      .addCase(fetchAthleteSportSectionsHide.rejected, (state, action) => {
        state.apiStatus = "";
        state.error = action.payload as string;
        state.toggleHighlightLinks = true;
        state.toggleVideoSection = true;
        state.toggleMileStone = true;
        state.toggleVictoryVault = true;
      })
      .addCase(fetchPublicAthleteSportExpose.pending, (state) => {
        state.apiStatus = "pblcExposePending";
      })
      .addCase(fetchPublicAthleteSportExpose.fulfilled, (state, action) => {
        state.apiStatus = "pblcExposeSuccess";
        state.publicAthleteSportToggles = action.payload && action.payload;
      })
      .addCase(fetchPublicAthleteSportExpose.rejected, (state, action) => {
        state.apiStatus = "pblcExposeFailed";
        state.error = action.payload as string;
        state.publicAthleteSportToggles = null
      });
  },
});

export const { handleUpdateUserInput } = athleteSportProfileSlice.actions;
export default athleteSportProfileSlice.reducer;
